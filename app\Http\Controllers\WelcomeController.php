<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Models\ServicesManagement;
use App\Models\Project;
use App\Models\ProjectsManagement;
use App\Models\HeroSection;
use App\Models\Skill;
use App\Models\SkillsManagement;
use App\Models\Testimonial;
use App\Models\TestimonialsContent;
use App\Models\Experience;
use App\Models\Education;
use App\Models\ResumeContent;
use App\Models\ContactManagement;
use App\Http\Controllers\Admin\FooterManagementController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Illuminate\Support\Facades\Schema;

class WelcomeController extends Controller
{
    public function index()
    {
        // Get the hero section data
        $heroSection = HeroSection::getOrCreate();
        
        // Get services for home page (always show 6 services if available)
        try {
            // First, get featured services
            $featuredServices = Service::where('is_active', true)
                ->where('is_featured', true)
                ->orderBy('order')
                ->get();

            // If we have less than 6 featured services, fill with non-featured ones
            $servicesNeeded = 6 - $featuredServices->count();

            if ($servicesNeeded > 0) {
                $nonFeaturedServices = Service::where('is_active', true)
                    ->where('is_featured', false)
                    ->orderBy('order')
                    ->take($servicesNeeded)
                    ->get();

                $services = $featuredServices->merge($nonFeaturedServices);
            } else {
                $services = $featuredServices->take(6);
            }

            // Get content management from dedicated table
            $contentManagement = ServicesManagement::getOrCreate();
        } catch (\Exception $e) {
            \Log::error('Error loading services in WelcomeController: ' . $e->getMessage());
            $services = collect();
            $contentManagement = null;
        }
        
        // Default content values if content management record is not available
        $defaultServicesContent = [
            'services_section_badge' => 'Professional Services',
            'services_section_title' => 'Areas of Expertise',
            'services_section_description' => 'Delivering tailored, high-quality solutions to help your business thrive in the digital landscape',
            'services_button_text' => 'Explore All Services',
        ];
        
        // Use content from the services management record
        $servicesContent = $contentManagement ? [
            'services_section_badge' => $contentManagement->services_section_badge,
            'services_section_title' => $contentManagement->services_section_title,
            'services_section_description' => $contentManagement->services_section_description,
            'services_button_text' => $contentManagement->services_button_text,
        ] : $defaultServicesContent;
        
        // Get first 6 published projects for home page
        $projects = Project::where('status', 'published')
            ->orderBy('id')
            ->take(6)
            ->get()
            ->map(function ($project) {
                return [
                    'id' => $project->id,
                    'title' => $project->title,
                    'description' => $project->description,
                    'image' => $project->image ? Storage::url($project->image) : null,
                    'category' => $project->category,
                    'status' => $project->status,
                    'is_featured' => $project->is_featured,
                    'completion_date' => $project->formatted_date,
                    'client_name' => $project->client_name,
                    'project_url' => $project->project_url,
                    'technologies' => $project->technologies,
                    'github_url' => $project->github_url,
                ];
            });
        
        // Get all testimonials for carousel display
        $testimonials = Testimonial::orderBy('order')
            ->get();

        // Get testimonials content management
        try {
            $testimonialsManagement = TestimonialsContent::getOrCreate();
        } catch (\Exception $e) {
            \Log::error('Error loading testimonials content in WelcomeController: ' . $e->getMessage());
            $testimonialsManagement = null;
        }
        
        // Get skills for home page display
        $progressSkills = Skill::progress()->get()->map(function($skill) {
            return [
                'label' => $skill->name,
                'percentage' => $skill->proficiency
            ];
        });

        $cardSkills = Skill::card()->get()->map(function($skill) {
            return [
                'skill' => $skill->name,
                'percentage' => $skill->proficiency
            ];
        });

        $skills = [
            'progress' => $progressSkills,
            'card' => $cardSkills
        ];
        
        // Get resume content first to get display limits
        try {
            $resumeContent = ResumeContent::getOrCreate();
        } catch (\Exception $e) {
            \Log::error('Error loading resume content in WelcomeController: ' . $e->getMessage());
            $resumeContent = null;
        }

        // Get experiences (use dynamic limit from resume content, only visible ones)
        $experienceLimit = $resumeContent ? $resumeContent->experience_display_limit : 3;
        $experiences = Experience::where('is_visible', true)
            ->orderBy('order')
            ->take($experienceLimit)
            ->get()
            ->map(function ($experience) {
                return [
                    'id' => $experience->id,
                    'title' => $experience->title,
                    'company' => $experience->company,
                    'location' => $experience->location,
                    'description' => $experience->description,
                    'is_current' => $experience->is_current,
                    'formatted_period' => $experience->formatted_period,
                    'start_date' => $experience->start_date,
                    'end_date' => $experience->end_date,
                ];
            });

        // Get education (use dynamic limit from resume content, only visible ones)
        $educationLimit = $resumeContent ? $resumeContent->education_display_limit : 3;
        $education = Education::where('is_visible', true)
            ->orderBy('order')
            ->take($educationLimit)
            ->get()
            ->map(function ($education) {
                return [
                    'id' => $education->id,
                    'degree' => $education->degree,
                    'institution' => $education->institution,
                    'location' => $education->location,
                    'description' => $education->description,
                    'formatted_period' => $education->formatted_period,
                    'start_date' => $education->start_date,
                    'end_date' => $education->end_date,
                ];
            });

        // Get projects content management
        try {
            $projectsManagement = ProjectsManagement::getOrCreate();
        } catch (\Exception $e) {
            \Log::error('Error loading projects content in WelcomeController: ' . $e->getMessage());
            $projectsManagement = null;
        }

        // Default content values if projects management record is not available
        $defaultProjectsContent = [
            'home_section_badge' => 'Portfolio',
            'home_section_title' => 'Featured Projects',
            'home_section_description' => 'Explore my latest work and see how I have helped clients achieve their goals',
            'home_section_button_text' => 'Explore All Projects',
        ];

        // Get skills content management
        try {
            $skillsManagement = SkillsManagement::getOrCreate();
        } catch (\Exception $e) {
            \Log::error('Error loading skills content in WelcomeController: ' . $e->getMessage());
            $skillsManagement = null;
        }

        // Default content values if skills management record is not available
        $defaultSkillsContent = [
            'skills_section_badge' => 'Skills',
            'skills_section_title' => 'Technical Proficiency',
            'skills_section_description' => 'I\'ve spent years honing my skills in various technologies and design principles',
        ];
        
        // Transform hero section data to ensure proper structure
        $heroSectionData = [
            'page_title' => $heroSection->page_title,
            'title' => $heroSection->title,
            'about' => $heroSection->about,
            'years_experience' => $heroSection->years_experience,
            'projects_completed' => $heroSection->projects_completed,
            'is_available' => $heroSection->is_available,
            'cta_text' => $heroSection->cta_text,
            'cta_secondary_text' => $heroSection->cta_secondary_text,
            'cta_url' => $heroSection->cta_url,
            'cta_secondary_url' => $heroSection->cta_secondary_url,
            'avatar' => $heroSection->avatar ? Storage::url($heroSection->avatar) : null,
            'resume' => $heroSection->resume,
            'logo' => [
                'text' => $heroSection->logo_text,
                'type' => $heroSection->logo_type,
                'icon' => $heroSection->logo_icon,
                'icon_type' => $heroSection->logo_icon_type,
                'image' => $heroSection->logo_image,
                'color' => $heroSection->logo_color,
            ],
            'navbar_items' => is_array($heroSection->navbar_items) ? $heroSection->navbar_items :
                (is_string($heroSection->navbar_items) ? json_decode($heroSection->navbar_items, true) : []),
            'hire_me' => [
                'text' => $heroSection->hire_me_text,
                'url' => $heroSection->hire_me_url,
                'enabled' => $heroSection->hire_me_enabled,
            ],
            'social' => $this->getTopSocialPlatforms($heroSection),
            'contact' => $this->getContactInformation(),
        ];

        // Get footer data
        $footerData = FooterManagementController::getFooterData();

        return Inertia::render('welcome', [
            'profile' => $heroSectionData,
            'services' => $services,
            'projects' => $projects,
            'testimonials' => $testimonials,
            'skills' => $skills,
            'experiences' => $experiences,
            'education' => $education,
            'resumeContent' => $resumeContent ? [
                'section_badge' => $resumeContent->section_badge,
                'section_title' => $resumeContent->section_title,
                'section_description' => $resumeContent->section_description,
                'experience_title' => $resumeContent->experience_title,
                'education_title' => $resumeContent->education_title,
                'download_button_text' => $resumeContent->download_button_text,
                'resume_file_path' => $resumeContent->resume_file_path,
            ] : [
                'section_badge' => 'Resume',
                'section_title' => 'Experience & Education',
                'section_description' => 'My professional journey and academic background',
                'experience_title' => 'Experience',
                'education_title' => 'Education',
                'download_button_text' => 'Download Full Resume',
                'resume_file_path' => null,
            ],
            'servicesContent' => [
                'badge' => $servicesContent['services_section_badge'],
                'title' => $servicesContent['services_section_title'],
                'description' => $servicesContent['services_section_description'],
                'button_text' => $servicesContent['services_button_text'],
            ],
            'projectsContent' => $projectsManagement ? [
                'badge' => $projectsManagement->home_section_badge,
                'title' => $projectsManagement->home_section_title,
                'description' => $projectsManagement->home_section_description,
                'button_text' => $projectsManagement->home_section_button_text,
                'filter_categories' => $projectsManagement->filter_categories ?: Project::getUniqueCategories(),
            ] : array_merge($defaultProjectsContent, [
                'filter_categories' => Project::getUniqueCategories()
            ]),
            'skillsContent' => $skillsManagement ? [
                'badge' => $skillsManagement->skills_section_badge,
                'title' => $skillsManagement->skills_section_title,
                'description' => $skillsManagement->skills_section_description,
            ] : $defaultSkillsContent,
            'testimonialsContent' => $testimonialsManagement ? [
                'badge' => $testimonialsManagement->section_badge,
                'title' => $testimonialsManagement->section_title,
                'description' => $testimonialsManagement->section_description,
            ] : [
                'badge' => 'Testimonials',
                'title' => 'What Clients Say',
                'description' => 'Feedback from clients who have experienced working with me.',
            ],
            'footerData' => $footerData,
        ]);
    }

    /**
     * Get the top 3 enabled social media platforms with URLs
     * Implements priority order: GitHub, LinkedIn, Twitter, then others
     */
    private function getTopSocialPlatforms($heroSection)
    {
        // Define platform priority order (most important first)
        $platformPriority = [
            'github' => ['enabled' => $heroSection->github_enabled, 'url' => $heroSection->github_url],
            'linkedin' => ['enabled' => $heroSection->linkedin_enabled, 'url' => $heroSection->linkedin_url],
            'twitter' => ['enabled' => $heroSection->twitter_enabled, 'url' => $heroSection->twitter_url],
            'facebook' => ['enabled' => $heroSection->facebook_enabled, 'url' => $heroSection->facebook_url],
            'instagram' => ['enabled' => $heroSection->instagram_enabled, 'url' => $heroSection->instagram_url],
            'dribbble' => ['enabled' => $heroSection->dribbble_enabled, 'url' => $heroSection->dribbble_url],
            'behance' => ['enabled' => $heroSection->behance_enabled, 'url' => $heroSection->behance_url],
            'upwork' => ['enabled' => $heroSection->upwork_enabled, 'url' => $heroSection->upwork_url],
            'fiverr' => ['enabled' => $heroSection->fiverr_enabled, 'url' => $heroSection->fiverr_url],
            'freelancer' => ['enabled' => $heroSection->freelancer_enabled, 'url' => $heroSection->freelancer_url],
            'peopleperhour' => ['enabled' => $heroSection->peopleperhour_enabled, 'url' => $heroSection->peopleperhour_url],
            'upassign' => ['enabled' => $heroSection->upassign_enabled, 'url' => $heroSection->upassign_url],
        ];

        // Filter enabled platforms with valid URLs and take only the first 3
        $enabledPlatforms = [];
        $count = 0;

        foreach ($platformPriority as $platform => $data) {
            if ($count >= 3) {
                break; // Stop after 3 platforms
            }

            if ($data['enabled'] && !empty(trim($data['url']))) {
                $enabledPlatforms[$platform] = $data['url'];
                $count++;
            }
        }

        // Fill remaining slots with null for consistency
        $allPlatforms = [
            'github' => null,
            'twitter' => null,
            'linkedin' => null,
            'facebook' => null,
            'instagram' => null,
            'dribbble' => null,
            'behance' => null,
            'upassign' => null,
            'fiverr' => null,
            'upwork' => null,
            'freelancer' => null,
            'peopleperhour' => null,
        ];

        // Merge enabled platforms with the default structure
        return array_merge($allPlatforms, $enabledPlatforms);
    }

    /**
     * Get contact information from ContactManagement
     */
    private function getContactInformation()
    {
        try {
            $contactManagement = ContactManagement::getOrCreate();
            return [
                'email' => $contactManagement->email,
                'phone' => $contactManagement->phone,
                'location' => $contactManagement->location,
                'section_badge' => $contactManagement->section_badge,
                'section_title' => $contactManagement->section_title,
                'section_description' => $contactManagement->section_description,
                'form_labels' => [
                    'name_label' => $contactManagement->form_name_label,
                    'name_placeholder' => $contactManagement->form_name_placeholder,
                    'email_label' => $contactManagement->form_email_label,
                    'email_placeholder' => $contactManagement->form_email_placeholder,
                    'subject_label' => $contactManagement->form_subject_label,
                    'subject_placeholder' => $contactManagement->form_subject_placeholder,
                    'message_label' => $contactManagement->form_message_label,
                    'message_placeholder' => $contactManagement->form_message_placeholder,
                    'submit_button_text' => $contactManagement->form_submit_button_text,
                ],
                'contact_labels' => [
                    'email_label' => $contactManagement->email_label,
                    'email_subtitle' => $contactManagement->email_subtitle,
                    'phone_label' => $contactManagement->phone_label,
                    'phone_subtitle' => $contactManagement->phone_subtitle,
                    'location_label' => $contactManagement->location_label,
                    'location_subtitle' => $contactManagement->location_subtitle,
                ],
            ];
        } catch (\Exception $e) {
            \Log::error('Error loading contact information in WelcomeController: ' . $e->getMessage());
            // Return default values if there's an error
            return [
                'email' => '<EMAIL>',
                'phone' => '+880 (781) 935014',
                'location' => 'Kushtia, Bangladesh',
                'section_badge' => 'Contact',
                'section_title' => 'Get In Touch',
                'section_description' => 'Have a project in mind? Let us discuss your ideas and bring them to life.',
                'form_labels' => [
                    'name_label' => 'Name',
                    'name_placeholder' => 'Your Name',
                    'email_label' => 'Email',
                    'email_placeholder' => '<EMAIL>',
                    'subject_label' => 'Subject',
                    'subject_placeholder' => 'Project Subject',
                    'message_label' => 'Message',
                    'message_placeholder' => 'Your Message',
                    'submit_button_text' => 'Send Message',
                ],
                'contact_labels' => [
                    'email_label' => 'Email',
                    'email_subtitle' => 'For general inquiries:',
                    'phone_label' => 'Phone',
                    'phone_subtitle' => 'Available Monday-Friday:',
                    'location_label' => 'Location',
                    'location_subtitle' => 'Based in:',
                ],
            ];
        }
    }
}