import { useState, useEffect } from 'react';
import { Head, useForm } from '@inertiajs/react';
import AdminLayout from '@/layouts/admin-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Trash2, Plus, Save, Eye, EyeOff } from 'lucide-react';
import { toast } from 'sonner';

interface FooterLink {
    title: string;
    url: string;
    enabled: boolean;
}

interface FooterData {
    id: number;
    description: string;
    copyright_text: string;
    footer_links: FooterLink[];
    show_social_links: boolean;
    show_footer_links: boolean;
    show_scroll_to_top: boolean;
    formatted_copyright: string;
    enabled_footer_links: FooterLink[];
}

interface Props {
    footer: FooterData;
    success?: string;
}

export default function FooterManagement({ footer, success }: Props) {
    const [activeTab, setActiveTab] = useState('content');

    // Content form
    const contentForm = useForm({
        description: footer.description,
        copyright_text: footer.copyright_text,
        show_social_links: footer.show_social_links,
        show_footer_links: footer.show_footer_links,
        show_scroll_to_top: footer.show_scroll_to_top,
    });

    // Footer links form
    const [footerLinks, setFooterLinks] = useState<FooterLink[]>(footer.footer_links || []);
    const footerLinksForm = useForm({
        footer_links: footerLinks
    });

    // Keep form data in sync with footerLinks state
    useEffect(() => {
        footerLinksForm.setData('footer_links', footerLinks);
    }, [footerLinks]);

    const handleContentSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        contentForm.post(route('admin.footer-management.content.update'), {
            onSuccess: () => {
                toast.success('Footer content updated successfully!');
            },
            onError: () => {
                toast.error('Failed to update footer content');
            }
        });
    };

    const handleFooterLinksSubmit = () => {
        footerLinksForm.post(route('admin.footer-management.links.update'), {
            onSuccess: () => {
                toast.success('Footer links updated successfully!');
            },
            onError: () => {
                toast.error('Failed to update footer links');
            }
        });
    };

    const addFooterLink = () => {
        // Check if we already have 3 enabled links
        const currentEnabledCount = footerLinks.filter(link => link.enabled).length;
        const shouldEnableNewLink = currentEnabledCount < 3;

        if (!shouldEnableNewLink) {
            toast.warning('New footer link added as disabled. Maximum 3 links can be active at once.');
        }

        setFooterLinks([...footerLinks, { title: '', url: '', enabled: shouldEnableNewLink }]);
    };

    const removeFooterLink = (index: number) => {
        setFooterLinks(footerLinks.filter((_, i) => i !== index));
    };

    const updateFooterLink = (index: number, field: keyof FooterLink, value: string | boolean) => {
        // If trying to enable a link, check if we already have 3 enabled links
        if (field === 'enabled' && value === true) {
            const currentEnabledCount = footerLinks.filter(link => link.enabled).length;
            if (currentEnabledCount >= 3) {
                toast.error('Maximum 3 footer links can be active at once. Please disable another link first.');
                return;
            }
        }

        const updated = [...footerLinks];
        updated[index] = { ...updated[index], [field]: value };
        setFooterLinks(updated);
    };



    return (
        <AdminLayout>
            <Head title="Footer Management - Portfolio Admin" />

            {/* Header */}
            <div className="border-b border-gray-200 bg-white">
                <div className="px-6 py-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-2xl font-semibold text-gray-900">Footer Management</h1>
                            <p className="mt-1 text-sm text-gray-500">Manage your website footer settings and content</p>
                        </div>
                        <Badge variant="outline" className="text-blue-600 border-blue-200">
                            {footer.enabled_footer_links.length} Footer Links Active
                        </Badge>
                    </div>
                </div>
            </div>

            {/* Content */}
            <div className="p-6">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="content">Footer Content</TabsTrigger>
                        <TabsTrigger value="links">Footer Links</TabsTrigger>
                    </TabsList>



                    {/* Footer Content Tab */}
                    <TabsContent value="content" className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Footer Content</CardTitle>
                                <CardDescription>
                                    Configure your footer description, copyright text, and display settings.
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleContentSubmit} className="space-y-6">
                                    <div className="space-y-2">
                                        <Label htmlFor="description">Footer Description</Label>
                                        <Textarea
                                            id="description"
                                            value={contentForm.data.description}
                                            onChange={(e) => contentForm.setData('description', e.target.value)}
                                            placeholder="Enter footer description"
                                            rows={3}
                                        />
                                        {contentForm.errors.description && (
                                            <p className="text-sm text-red-600">{contentForm.errors.description}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="copyright_text">Copyright Text</Label>
                                        <Input
                                            id="copyright_text"
                                            value={contentForm.data.copyright_text}
                                            onChange={(e) => contentForm.setData('copyright_text', e.target.value)}
                                            placeholder="© {year}. All rights reserved."
                                        />
                                        <p className="text-xs text-gray-500">
                                            Use {'{year}'} for current year
                                        </p>
                                        {contentForm.errors.copyright_text && (
                                            <p className="text-sm text-red-600">{contentForm.errors.copyright_text}</p>
                                        )}
                                    </div>

                                    <div className="space-y-4">
                                        <h3 className="text-lg font-medium">Display Settings</h3>
                                        <div className="space-y-4">
                                            <div className="flex items-center justify-between">
                                                <div>
                                                    <Label htmlFor="show_social_links">Show Social Links</Label>
                                                    <p className="text-sm text-gray-500">Display social media links in footer</p>
                                                </div>
                                                <Switch
                                                    id="show_social_links"
                                                    checked={contentForm.data.show_social_links}
                                                    onCheckedChange={(checked) => contentForm.setData('show_social_links', checked)}
                                                />
                                            </div>

                                            <div className="flex items-center justify-between">
                                                <div>
                                                    <Label htmlFor="show_footer_links">Show Footer Links</Label>
                                                    <p className="text-sm text-gray-500">Display footer navigation links</p>
                                                </div>
                                                <Switch
                                                    id="show_footer_links"
                                                    checked={contentForm.data.show_footer_links}
                                                    onCheckedChange={(checked) => contentForm.setData('show_footer_links', checked)}
                                                />
                                            </div>

                                            <div className="flex items-center justify-between">
                                                <div>
                                                    <Label htmlFor="show_scroll_to_top">Show Scroll to Top Button</Label>
                                                    <p className="text-sm text-gray-500">Display scroll to top button</p>
                                                </div>
                                                <Switch
                                                    id="show_scroll_to_top"
                                                    checked={contentForm.data.show_scroll_to_top}
                                                    onCheckedChange={(checked) => contentForm.setData('show_scroll_to_top', checked)}
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    <div className="flex justify-end">
                                        <Button type="submit" disabled={contentForm.processing} className="bg-[#20B2AA] hover:bg-[#1a9994]">
                                            <Save className="w-4 h-4 mr-2" />
                                            Save Footer Content
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </TabsContent>



                    {/* Footer Links Tab */}
                    <TabsContent value="links" className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center justify-between">
                                    Footer Links
                                    <Badge variant="outline" className={`${footerLinks.filter(link => link.enabled).length >= 3 ? 'text-orange-600 border-orange-200' : 'text-green-600 border-green-200'}`}>
                                        {footerLinks.filter(link => link.enabled).length}/3 Active
                                    </Badge>
                                </CardTitle>
                                <CardDescription>
                                    Manage footer navigation links like Privacy Policy, Terms of Service, etc. Maximum 3 links can be active at once.
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-6">
                                    <div className="space-y-4">
                                        {footerLinks.map((link, index) => {
                                            const enabledCount = footerLinks.filter(l => l.enabled).length;
                                            const isAtLimit = enabledCount >= 3 && !link.enabled;

                                            return (
                                                <div key={index} className={`flex items-center gap-4 p-4 border rounded-lg ${!link.enabled ? 'bg-gray-50 border-gray-200' : 'bg-white border-gray-300'}`}>
                                                    <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                                                        <Input
                                                            value={link.title}
                                                            onChange={(e) => updateFooterLink(index, 'title', e.target.value)}
                                                            placeholder="Link title"
                                                            className={!link.enabled ? 'bg-gray-100' : ''}
                                                        />
                                                        <Input
                                                            value={link.url}
                                                            onChange={(e) => updateFooterLink(index, 'url', e.target.value)}
                                                            placeholder="Link URL"
                                                            className={!link.enabled ? 'bg-gray-100' : ''}
                                                        />
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <div className="flex flex-col items-center gap-1">
                                                            <Switch
                                                                checked={link.enabled}
                                                                onCheckedChange={(checked) => updateFooterLink(index, 'enabled', checked)}
                                                            />
                                                            {!link.enabled && isAtLimit && (
                                                                <span className="text-xs text-orange-600">Limit reached</span>
                                                            )}
                                                        </div>
                                                        <Button
                                                            type="button"
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => removeFooterLink(index)}
                                                            className="text-red-600 hover:text-red-700"
                                                        >
                                                            <Trash2 className="w-4 h-4" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={addFooterLink}
                                        >
                                            <Plus className="w-4 h-4 mr-2" />
                                            Add Footer Link
                                        </Button>

                                        <Button
                                            type="button"
                                            onClick={handleFooterLinksSubmit}
                                            className="bg-[#20B2AA] hover:bg-[#1a9994]"
                                        >
                                            <Save className="w-4 h-4 mr-2" />
                                            Save Footer Links
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AdminLayout>
    );
}
