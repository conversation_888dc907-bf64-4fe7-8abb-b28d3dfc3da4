# CV/Resume Upload and Download Implementation

## Overview
This implementation provides a complete CV/resume upload and download system that connects the admin profile settings with the hero section download buttons on the homepage.

## Features Implemented

### 1. Admin Profile Settings
- **Upload Resume**: Upload CV/resume files (PDF, DOC, DOCX) with 10MB size limit
- **File Validation**: Server-side validation for file types and size
- **Current File Status**: Shows when a resume is already uploaded
- **Download Current**: Download the currently uploaded resume
- **Delete Current**: Remove the currently uploaded resume
- **Replace File**: Upload a new file to replace the existing one

### 2. Hero Section Integration
- **Download CV Button**: Downloads the resume uploaded in admin profile settings
- **View Portfolio Button**: Works as configured (uses `ctaUrl` setting)
- **Smart Error Handling**: Shows appropriate message when no resume is available

### 3. Technical Implementation

#### Backend Routes
```php
// Public download route
Route::get('/download-hero-resume', [ProfileController::class, 'downloadHeroResume']);

// Admin delete route
Route::delete('/admin/profile/delete-resume', [ProfileController::class, 'deleteHeroResume']);
```

#### Controller Methods
- `downloadHeroResume()`: Downloads resume from HeroSection model
- `deleteHeroResume()`: Deletes resume file and updates database
- File validation for uploads (PDF, DOC, DOCX, max 10MB)

#### Frontend Integration
- Hero section "Download CV" button checks for `profile.resume` availability
- Admin profile page shows current file status with download/delete options
- Proper error handling and user feedback

## How It Works

### For Admin Users:
1. Go to `/admin/profile`
2. Upload a CV/resume file in the "Resume/CV" section
3. File is validated and stored in `storage/app/public/resumes/`
4. Current file status is displayed with download/delete options
5. Can replace file by uploading a new one

### For Website Visitors:
1. Visit the homepage
2. Click "Download CV" button in the hero section
3. If resume is uploaded, file downloads automatically
4. If no resume, shows appropriate error message

### For "View Portfolio" Button:
- Uses the `ctaUrl` setting from admin profile
- Can be configured to scroll to any section or external URL
- Default behavior is to scroll to works/portfolio section

## File Storage
- **Location**: `storage/app/public/resumes/`
- **Access**: Files are accessible via `/storage/resumes/filename`
- **Cleanup**: Old files are automatically deleted when new ones are uploaded

## Database Schema
The resume file path is stored in the `hero_sections` table:
```sql
resume VARCHAR(255) NULL  -- Stores the file path relative to storage/app/public/
```

## Error Handling
- **File Not Found**: Returns 404 with JSON error message
- **Invalid File Type**: Validation error with specific message
- **File Too Large**: Validation error for files over 10MB
- **No File Uploaded**: User-friendly alert message

## Security Considerations
- File type validation prevents malicious uploads
- File size limits prevent storage abuse
- Files stored outside web root for security
- CSRF protection on all admin routes

## Testing the Implementation
1. **Upload Test**: Go to admin profile and upload a resume
2. **Download Test**: Click "Download CV" on homepage
3. **Delete Test**: Remove resume from admin panel
4. **Error Test**: Try downloading when no file exists
5. **Replace Test**: Upload a new file to replace existing one

## Future Enhancements
- Multiple resume versions (e.g., different languages)
- Resume preview in admin panel
- Download analytics/tracking
- Resume template generation
