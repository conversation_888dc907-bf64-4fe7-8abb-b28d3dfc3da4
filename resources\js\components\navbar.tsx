import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X } from 'lucide-react';
import { Link } from '@inertiajs/react';
import { DynamicLogo } from '@/components/ui/dynamic-logo';
import { ThemeToggle } from '@/components/theme-toggle';
import { MobileMenu } from '@/components/ui/mobile-menu';

interface NavbarItem {
    title: string;
    href: string;
}

interface Profile {
    page_title: string;
    logo_text: string;
    logo_type: string;
    logo_icon: string;
    logo_icon_type: string;
    logo_image: string | null;
    logo_color: string;
    navbar_items: NavbarItem[];
    hire_me_enabled: boolean;
    hire_me_text: string;
    hire_me_url: string;
}

interface NavbarProps {
    profile: Profile;
}

export function Navbar({ profile }: NavbarProps) {
    const [isScrolled, setIsScrolled] = useState(false);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 50);
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    const navItems = profile.navbar_items || [
        { title: 'Home', href: '/' },
        { title: 'Services', href: '/services' },
        { title: 'Projects', href: '/projects' },
        { title: 'Pages', href: '/pages' },
        { title: 'Contact', href: '/#contact' }
    ];

    return (
        <>
            <motion.nav
                initial={{ y: -100 }}
                animate={{ y: 0 }}
                className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
                    isScrolled
                        ? 'bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200/50 dark:border-gray-700/50'
                        : 'bg-transparent'
                }`}
            >
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex items-center justify-between h-16 lg:h-20">
                        {/* Logo */}
                        <Link href="/" className="flex items-center gap-2 group">
                            <DynamicLogo
                                logoType={profile.logo_type}
                                logoText={profile.logo_text}
                                logoIcon={profile.logo_icon}
                                logoIconType={profile.logo_icon_type}
                                logoImage={profile.logo_image}
                                logoColor={profile.logo_color}
                                className="transition-transform duration-300 group-hover:scale-105"
                            />
                        </Link>

                        {/* Desktop Navigation */}
                        <div className="hidden lg:flex items-center gap-8">
                            <div className="flex items-center gap-6">
                                {navItems.map((item, index) => (
                                    <motion.div
                                        key={item.title}
                                        initial={{ opacity: 0, y: -20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: index * 0.1 }}
                                    >
                                        {item.href.startsWith('#') ? (
                                            <a
                                                href={item.href}
                                                className="text-gray-700 dark:text-gray-300 hover:text-[#20B2AA] dark:hover:text-[#20B2AA] font-medium transition-colors duration-300 relative group"
                                            >
                                                {item.title}
                                                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-[#20B2AA] transition-all duration-300 group-hover:w-full"></span>
                                            </a>
                                        ) : (
                                            <Link
                                                href={item.href}
                                                className="text-gray-700 dark:text-gray-300 hover:text-[#20B2AA] dark:hover:text-[#20B2AA] font-medium transition-colors duration-300 relative group"
                                            >
                                                {item.title}
                                                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-[#20B2AA] transition-all duration-300 group-hover:w-full"></span>
                                            </Link>
                                        )}
                                    </motion.div>
                                ))}
                            </div>

                            <div className="flex items-center gap-4">
                                <ThemeToggle />
                                
                                {profile.hire_me_enabled && (
                                    <motion.div
                                        initial={{ opacity: 0, scale: 0.8 }}
                                        animate={{ opacity: 1, scale: 1 }}
                                        transition={{ delay: 0.5 }}
                                    >
                                        <Link
                                            href={profile.hire_me_url}
                                            className="bg-[#20B2AA] hover:bg-[#1a9994] text-white px-6 py-2.5 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 transform"
                                        >
                                            {profile.hire_me_text}
                                        </Link>
                                    </motion.div>
                                )}
                            </div>
                        </div>

                        {/* Mobile Menu Button */}
                        <div className="lg:hidden flex items-center gap-3">
                            <ThemeToggle />
                            <motion.button
                                whileTap={{ scale: 0.95 }}
                                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                                className="p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-300"
                                aria-label="Toggle mobile menu"
                            >
                                <AnimatePresence mode="wait">
                                    {isMobileMenuOpen ? (
                                        <motion.div
                                            key="close"
                                            initial={{ rotate: -90, opacity: 0 }}
                                            animate={{ rotate: 0, opacity: 1 }}
                                            exit={{ rotate: 90, opacity: 0 }}
                                            transition={{ duration: 0.2 }}
                                        >
                                            <X className="w-6 h-6" />
                                        </motion.div>
                                    ) : (
                                        <motion.div
                                            key="menu"
                                            initial={{ rotate: 90, opacity: 0 }}
                                            animate={{ rotate: 0, opacity: 1 }}
                                            exit={{ rotate: -90, opacity: 0 }}
                                            transition={{ duration: 0.2 }}
                                        >
                                            <Menu className="w-6 h-6" />
                                        </motion.div>
                                    )}
                                </AnimatePresence>
                            </motion.button>
                        </div>
                    </div>
                </div>
            </motion.nav>

            {/* Mobile Menu */}
            <MobileMenu
                isOpen={isMobileMenuOpen}
                onClose={() => setIsMobileMenuOpen(false)}
                navItems={navItems}
                logoText={profile.logo_text}
                logoType={profile.logo_type}
                logoIcon={profile.logo_icon}
                logoColor={profile.logo_color}
                hireMeEnabled={profile.hire_me_enabled}
                hireMeText={profile.hire_me_text}
                hireMeUrl={profile.hire_me_url}
            />
        </>
    );
}
