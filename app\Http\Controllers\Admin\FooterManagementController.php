<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FooterManagement;
use App\Models\HeroSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Inertia\Inertia;

class FooterManagementController extends Controller
{
    /**
     * Display the footer management page
     */
    public function index()
    {
        try {
            $footer = FooterManagement::getOrCreate();
        } catch (\Exception $e) {
            // If table doesn't exist, create it
            \Artisan::call('setup:footer-management');
            $footer = FooterManagement::getOrCreate();
        }

        return Inertia::render('admin/footer-management', [
            'footer' => [
                'id' => $footer->id,

                // Footer Content
                'description' => $footer->description,
                'copyright_text' => $footer->copyright_text,

                // Footer Links
                'footer_links' => $footer->footer_links ?? [],

                // Settings
                'show_social_links' => $footer->show_social_links,
                'show_footer_links' => $footer->show_footer_links,
                'show_scroll_to_top' => $footer->show_scroll_to_top,

                // Computed
                'formatted_copyright' => $footer->formatted_copyright,
                'enabled_footer_links' => $footer->enabled_footer_links,
            ]
        ]);
    }

    /**
     * Setup footer management table and data
     */
    public function setup()
    {
        try {
            Artisan::call('setup:footer-management');
            return redirect()->route('admin.footer-management')->with('success', 'Footer Management setup completed successfully!');
        } catch (\Exception $e) {
            return redirect()->route('admin.dashboard')->with('error', 'Failed to setup Footer Management: ' . $e->getMessage());
        }
    }



    /**
     * Update footer content
     */
    public function updateContent(Request $request)
    {
        $validated = $request->validate([
            'description' => 'required|string|max:1000',
            'copyright_text' => 'required|string|max:255',
            'show_social_links' => 'boolean',
            'show_footer_links' => 'boolean',
            'show_scroll_to_top' => 'boolean',
        ]);

        $footer = FooterManagement::getOrCreate();
        $footer->update($validated);

        return back()->with('success', 'Footer content updated successfully!');
    }



    /**
     * Update footer links
     */
    public function updateFooterLinks(Request $request)
    {
        $validated = $request->validate([
            'footer_links' => 'required|array',
            'footer_links.*.title' => 'required|string|max:255',
            'footer_links.*.url' => 'required|string|max:255',
            'footer_links.*.enabled' => 'boolean',
        ]);

        // Enforce maximum 3 enabled footer links
        $enabledCount = 0;
        foreach ($validated['footer_links'] as &$link) {
            if ($link['enabled']) {
                $enabledCount++;
                if ($enabledCount > 3) {
                    $link['enabled'] = false; // Automatically disable excess links
                }
            }
        }

        $footer = FooterManagement::getOrCreate();
        $footer->update($validated);

        $message = 'Footer links updated successfully!';
        if ($enabledCount > 3) {
            $message .= ' Note: Only the first 3 enabled links were kept active.';
        }

        return back()->with('success', $message);
    }

    /**
     * Get footer data for public use
     */
    public static function getFooterData()
    {
        $footer = FooterManagement::getOrCreate();
        $heroSection = HeroSection::getOrCreate();

        // Get social links from hero section
        $socialLinks = self::getHeroSocialLinks($heroSection);

        return [
            'logo' => [
                'text' => $heroSection->logo_text,
                'type' => $heroSection->logo_type,
                'icon' => $heroSection->logo_icon,
                'icon_type' => $heroSection->logo_icon_type,
                'image' => $heroSection->logo_image,
                'color' => $heroSection->logo_color,
            ],
            'description' => $footer->description,
            'copyright' => $footer->formatted_copyright,
            'company_name' => $footer->company_name,
            'social_links' => $socialLinks,
            'footer_links' => $footer->enabled_footer_links,
            'settings' => [
                'show_social_links' => $footer->show_social_links,
                'show_footer_links' => $footer->show_footer_links,
                'show_scroll_to_top' => $footer->show_scroll_to_top,
            ],
        ];
    }

    /**
     * Get social links from hero section
     * Uses the same order as the hero section UI rendering (GitHub, Twitter, LinkedIn)
     */
    private static function getHeroSocialLinks($heroSection)
    {
        // Define platform priority order (same as hero section UI rendering)
        $platformPriority = [
            'github' => ['enabled' => $heroSection->github_enabled, 'url' => $heroSection->github_url],
            'twitter' => ['enabled' => $heroSection->twitter_enabled, 'url' => $heroSection->twitter_url],
            'linkedin' => ['enabled' => $heroSection->linkedin_enabled, 'url' => $heroSection->linkedin_url],
            'facebook' => ['enabled' => $heroSection->facebook_enabled, 'url' => $heroSection->facebook_url],
            'instagram' => ['enabled' => $heroSection->instagram_enabled, 'url' => $heroSection->instagram_url],
            'dribbble' => ['enabled' => $heroSection->dribbble_enabled, 'url' => $heroSection->dribbble_url],
            'behance' => ['enabled' => $heroSection->behance_enabled, 'url' => $heroSection->behance_url],
            'upwork' => ['enabled' => $heroSection->upwork_enabled, 'url' => $heroSection->upwork_url],
            'fiverr' => ['enabled' => $heroSection->fiverr_enabled, 'url' => $heroSection->fiverr_url],
            'freelancer' => ['enabled' => $heroSection->freelancer_enabled, 'url' => $heroSection->freelancer_url],
            'peopleperhour' => ['enabled' => $heroSection->peopleperhour_enabled, 'url' => $heroSection->peopleperhour_url],
            'upassign' => ['enabled' => $heroSection->upassign_enabled, 'url' => $heroSection->upassign_url],
        ];

        $socialPlatformConfig = [
            'github' => ['icon' => 'Github', 'color' => 'hover:text-gray-900 dark:hover:text-white'],
            'twitter' => ['icon' => 'Twitter', 'color' => 'hover:text-blue-400'],
            'linkedin' => ['icon' => 'Linkedin', 'color' => 'hover:text-blue-600'],
            'facebook' => ['icon' => 'Facebook', 'color' => 'hover:text-blue-600'],
            'instagram' => ['icon' => 'Instagram', 'color' => 'hover:text-pink-600'],
            'dribbble' => ['icon' => 'Dribbble', 'color' => 'hover:text-pink-500'],
            'behance' => ['icon' => 'Behance', 'color' => 'hover:text-blue-500'],
            'upwork' => ['icon' => 'Upwork', 'color' => 'hover:text-green-600'],
            'fiverr' => ['icon' => 'Fiverr', 'color' => 'hover:text-green-500'],
            'freelancer' => ['icon' => 'Freelancer', 'color' => 'hover:text-blue-500'],
            'peopleperhour' => ['icon' => 'PeoplePerHour', 'color' => 'hover:text-orange-500'],
            'upassign' => ['icon' => 'UPassign', 'color' => 'hover:text-green-500'],
        ];

        $socialLinks = [];

        foreach ($platformPriority as $platform => $data) {
            if ($data['enabled'] && !empty($data['url'])) {
                $config = $socialPlatformConfig[$platform];
                $socialLinks[] = [
                    'name' => ucfirst($platform),
                    'url' => $data['url'],
                    'icon' => $config['icon'],
                    'color' => $config['color'],
                ];
            }
        }

        return $socialLinks;
    }
}
