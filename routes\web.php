<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\SkillController;
use App\Http\Controllers\ExperienceController;
use App\Http\Controllers\EducationController;
use App\Http\Controllers\ResumeContentController;
use App\Http\Controllers\TestimonialController;
use App\Http\Controllers\TestimonialsContentController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\WelcomeController;
use App\Http\Controllers\Settings\ProfileController as SettingsProfileController;
use App\Http\Controllers\Settings\PasswordController as SettingsPasswordController;
use Illuminate\Support\Facades\Artisan;

Route::get('/', [WelcomeController::class, 'index'])->name('home');

// Public resume download routes
Route::get('/download-resume', [ResumeContentController::class, 'downloadResume'])->name('resume.download');
Route::get('/download-hero-resume', [App\Http\Controllers\Admin\ProfileController::class, 'downloadHeroResume'])->name('hero.resume.download');

// Guest routes
Route::middleware('guest')->group(function () {
    Route::get('/login', function () {
        return Inertia::render('auth/login');
    })->name('login');
});

// Dashboard redirect route for tests
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // User settings routes
    Route::get('/settings/profile', [SettingsProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/settings/profile', [SettingsProfileController::class, 'update'])->name('profile.update');
    Route::delete('/settings/profile', [SettingsProfileController::class, 'destroy'])->name('profile.destroy');

    Route::get('/settings/password', [SettingsPasswordController::class, 'edit'])->name('password.edit');
    Route::put('/settings/password', [SettingsPasswordController::class, 'update'])->name('password.update');
});

// Admin routes
Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // Profile routes
    Route::get('/profile', [App\Http\Controllers\Admin\ProfileController::class, 'index'])->name('profile');
    Route::post('/profile', [App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('profile.update');
    Route::post('/profile/hero', [App\Http\Controllers\Admin\ProfileController::class, 'updateHeroSection'])->name('profile.hero.update');
    Route::post('/profile/social', [App\Http\Controllers\Admin\ProfileController::class, 'updateSocialLinks'])->name('profile.social.update');
    Route::delete('/profile/delete-resume', [App\Http\Controllers\Admin\ProfileController::class, 'deleteHeroResume'])->name('profile.resume.delete');
    
    // Navbar settings routes
    Route::get('/navbar', [App\Http\Controllers\Admin\NavbarController::class, 'index'])->name('navbar');
    Route::post('/navbar/logo', [App\Http\Controllers\Admin\NavbarController::class, 'updateLogo'])->name('navbar.logo.update');
    Route::post('/navbar/items', [App\Http\Controllers\Admin\NavbarController::class, 'updateNavbarItems'])->name('navbar.items.update');

    // Services routes
    Route::get('/services', [ServiceController::class, 'index'])->name('services');
    Route::post('/services', [ServiceController::class, 'store'])->name('services.store');
    Route::put('/services/{service}', [ServiceController::class, 'update'])->name('services.update');
    Route::delete('/services/{service}', [ServiceController::class, 'destroy'])->name('services.destroy');
    Route::post('/services/reorder', [ServiceController::class, 'reorder'])->name('services.reorder');
    Route::post('/services/content', [ServiceController::class, 'updateContent'])->name('services.content.update');
    // Route::post('/services/reset-ids', [ServiceController::class, 'resetIds'])->name('services.reset-ids');

    // Projects routes
    Route::get('/projects', [ProjectController::class, 'index'])->name('projects');
    Route::post('/projects', [ProjectController::class, 'store'])->name('projects.store');
    Route::put('/projects/{project}', [ProjectController::class, 'update'])->name('projects.update');
    Route::delete('/projects/{project}', [ProjectController::class, 'destroy'])->name('projects.destroy');
    Route::post('/projects/{project}/toggle-featured', [ProjectController::class, 'toggleFeatured'])->name('projects.toggle-featured');
    Route::post('/projects/content', [ProjectController::class, 'updateContent'])->name('projects.content.update');

    // Skills routes
    Route::get('/skills', [SkillController::class, 'index'])->name('skills');
    Route::post('/skills', [SkillController::class, 'store'])->name('skills.store');
    Route::put('/skills/{skill}', [SkillController::class, 'update'])->name('skills.update');
    Route::delete('/skills/{skill}', [SkillController::class, 'destroy'])->name('skills.destroy');
    Route::post('/skills/reorder', [SkillController::class, 'reorder'])->name('skills.reorder');
    Route::post('/skills/{skill}/toggle-visibility', [SkillController::class, 'toggleVisibility'])->name('skills.toggle-visibility');
    Route::post('/skills/content', [SkillController::class, 'updateContent'])->name('skills.content.update');

    // Experience routes
    Route::get('/experiences', [ExperienceController::class, 'index'])->name('experiences.index');
    Route::post('/experiences', [ExperienceController::class, 'store'])->name('experiences.store');
    Route::put('/experiences/{experience}', [ExperienceController::class, 'update'])->name('experiences.update');
    Route::delete('/experiences/{experience}', [ExperienceController::class, 'destroy'])->name('experiences.destroy');
    Route::post('/experiences/reorder', [ExperienceController::class, 'reorder'])->name('experiences.reorder');
    Route::post('/experiences/{experience}/toggle-visibility', [ExperienceController::class, 'toggleVisibility'])->name('experiences.toggle-visibility');
    Route::post('/experiences/batch-update', [ExperienceController::class, 'batchUpdate'])->name('experiences.batch-update');

    // Education routes
    Route::get('/education', [EducationController::class, 'index'])->name('education.index');
    Route::post('/education', [EducationController::class, 'store'])->name('education.store');
    Route::put('/education/{education}', [EducationController::class, 'update'])->name('education.update');
    Route::delete('/education/{education}', [EducationController::class, 'destroy'])->name('education.destroy');
    Route::post('/education/reorder', [EducationController::class, 'reorder'])->name('education.reorder');
    Route::post('/education/{education}/toggle-visibility', [EducationController::class, 'toggleVisibility'])->name('education.toggle-visibility');
    Route::post('/education/batch-update', [EducationController::class, 'batchUpdate'])->name('education.batch-update');

    // Resume Content routes
    Route::get('/resume-content', [ResumeContentController::class, 'index'])->name('resume-content.index');
    Route::put('/resume-content', [ResumeContentController::class, 'update'])->name('resume-content.update');
    Route::post('/resume-content/upload', [ResumeContentController::class, 'uploadResume'])->name('resume-content.upload');
    Route::delete('/resume-content/file', [ResumeContentController::class, 'deleteResume'])->name('resume-content.delete-file');

    Route::get('/resume', function () {
        return Inertia::render('resume');
    })->name('resume');

    // Testimonials routes
    Route::get('/testimonials', [TestimonialController::class, 'index'])->name('testimonials');
    Route::post('/testimonials', [TestimonialController::class, 'store'])->name('testimonials.store');
    Route::put('/testimonials/{testimonial}', [TestimonialController::class, 'update'])->name('testimonials.update');
    Route::delete('/testimonials/{testimonial}', [TestimonialController::class, 'destroy'])->name('testimonials.destroy');
    Route::post('/testimonials/reorder', [TestimonialController::class, 'reorder'])->name('testimonials.reorder');

    // Testimonials Content routes
    Route::get('/testimonials-content', [TestimonialsContentController::class, 'index'])->name('testimonials-content.index');
    Route::put('/testimonials-content', [TestimonialsContentController::class, 'update'])->name('testimonials-content.update');

    // Contact routes
    Route::get('/contact', [ContactController::class, 'index'])->name('contact');
    Route::put('/contact/management', [ContactController::class, 'updateManagement'])->name('contact.management.update');
    Route::get('/contact/messages/{message}', [ContactController::class, 'show'])->name('contact.messages.show');
    Route::post('/contact/messages/{message}/read', [ContactController::class, 'markAsRead'])->name('contact.messages.read');
    Route::post('/contact/messages/{message}/unread', [ContactController::class, 'markAsUnread'])->name('contact.messages.unread');
    Route::post('/contact/messages/{message}/reply', [ContactController::class, 'reply'])->name('contact.messages.reply');
    Route::post('/contact/messages/{message}/archive', [ContactController::class, 'archive'])->name('contact.messages.archive');
    Route::post('/contact/messages/{message}/unarchive', [ContactController::class, 'unarchive'])->name('contact.messages.unarchive');
    Route::delete('/contact/messages/{message}', [ContactController::class, 'destroy'])->name('contact.messages.destroy');

    Route::get('/appearance', function () {
        return Inertia::render('admin/appearance');
    })->name('appearance');

    Route::get('/settings', [App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('settings');
    Route::post('/settings', [App\Http\Controllers\Admin\SettingsController::class, 'update'])->name('settings.update');
    Route::post('/settings/clear-cache', [App\Http\Controllers\Admin\SettingsController::class, 'clearCache'])->name('settings.clear-cache');
    Route::post('/settings/export-data', [App\Http\Controllers\Admin\SettingsController::class, 'exportData'])->name('settings.export-data');

    // SMTP Configuration routes
    Route::get('/smtp-config', [App\Http\Controllers\Admin\SmtpConfigurationController::class, 'index'])->name('smtp-config');
    Route::put('/smtp-config', [App\Http\Controllers\Admin\SmtpConfigurationController::class, 'update'])->name('smtp-config.update');
    Route::post('/smtp-config/test', [App\Http\Controllers\Admin\SmtpConfigurationController::class, 'test'])->name('smtp-config.test');
    Route::get('/smtp-config/preset/{provider}', [App\Http\Controllers\Admin\SmtpConfigurationController::class, 'getProviderPreset'])->name('smtp-config.preset');

    // Page Management routes
    Route::resource('pages', App\Http\Controllers\Admin\PageController::class)->parameters(['pages' => 'page:id']);
    Route::post('/pages/{id}/toggle-status', [App\Http\Controllers\Admin\PageController::class, 'toggleStatus'])->name('pages.toggle-status')->where('id', '[0-9]+');
    Route::post('/pages/{id}/toggle-featured', [App\Http\Controllers\Admin\PageController::class, 'toggleFeatured'])->name('pages.toggle-featured')->where('id', '[0-9]+');
    Route::post('/pages/reorder', [App\Http\Controllers\Admin\PageController::class, 'reorder'])->name('pages.reorder');

    // Footer Management routes
    Route::get('/footer-management', [App\Http\Controllers\Admin\FooterManagementController::class, 'index'])->name('footer-management');
    Route::get('/footer-management/setup', [App\Http\Controllers\Admin\FooterManagementController::class, 'setup'])->name('footer-management.setup');
    Route::post('/footer-management/content', [App\Http\Controllers\Admin\FooterManagementController::class, 'updateContent'])->name('footer-management.content.update');
    Route::post('/footer-management/links', [App\Http\Controllers\Admin\FooterManagementController::class, 'updateFooterLinks'])->name('footer-management.links.update');
});

Route::get('/services', [ServiceController::class, 'publicIndex'])->name('services');
Route::get('/projects', [ProjectController::class, 'publicIndex'])->name('projects');
Route::get('/projects/{project}', [ProjectController::class, 'show'])->name('projects.show');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

// Public page routes
Route::get('/pages', [App\Http\Controllers\PageController::class, 'index'])->name('pages.index');
Route::get('/page/{slug}', [App\Http\Controllers\PageController::class, 'show'])->name('pages.show');

require __DIR__.'/auth.php';
