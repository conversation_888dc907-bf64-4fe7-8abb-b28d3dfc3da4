<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class SetupFooterManagement extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'setup:footer-management';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup footer management table and default data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up Footer Management...');

        // Check if the table exists
        if (!Schema::hasTable('footer_management')) {
            $this->info('Creating footer_management table...');
            
            DB::statement("
                CREATE TABLE footer_management (
                    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
                    logo_text VARCHAR(255) NOT NULL DEFAULT 'Portfolio',
                    logo_type VARCHAR(255) NOT NULL DEFAULT 'text_with_icon',
                    logo_icon VARCHAR(255) NOT NULL DEFAULT 'P',
                    logo_icon_type VARCHAR(255) NOT NULL DEFAULT 'letter',
                    logo_color VARCHAR(255) NOT NULL DEFAULT '#20B2AA',
                    description TEXT NOT NULL DEFAULT 'Creating exceptional digital experiences through innovative design and development solutions.',
                    copyright_text VARCHAR(255) NOT NULL DEFAULT '© {year} {company}. All rights reserved.',
                    company_name VARCHAR(255) NOT NULL DEFAULT 'Your Portfolio',
                    twitter_url VARCHAR(255) NULL,
                    github_url VARCHAR(255) NULL,
                    linkedin_url VARCHAR(255) NULL,
                    facebook_url VARCHAR(255) NULL,
                    instagram_url VARCHAR(255) NULL,
                    dribbble_url VARCHAR(255) NULL,
                    behance_url VARCHAR(255) NULL,
                    upassign_url VARCHAR(255) NULL,
                    fiverr_url VARCHAR(255) NULL,
                    upwork_url VARCHAR(255) NULL,
                    freelancer_url VARCHAR(255) NULL,
                    peopleperhour_url VARCHAR(255) NULL,
                    twitter_enabled TINYINT(1) NOT NULL DEFAULT 0,
                    github_enabled TINYINT(1) NOT NULL DEFAULT 0,
                    linkedin_enabled TINYINT(1) NOT NULL DEFAULT 0,
                    facebook_enabled TINYINT(1) NOT NULL DEFAULT 0,
                    instagram_enabled TINYINT(1) NOT NULL DEFAULT 0,
                    dribbble_enabled TINYINT(1) NOT NULL DEFAULT 0,
                    behance_enabled TINYINT(1) NOT NULL DEFAULT 0,
                    upassign_enabled TINYINT(1) NOT NULL DEFAULT 0,
                    fiverr_enabled TINYINT(1) NOT NULL DEFAULT 0,
                    upwork_enabled TINYINT(1) NOT NULL DEFAULT 0,
                    freelancer_enabled TINYINT(1) NOT NULL DEFAULT 0,
                    peopleperhour_enabled TINYINT(1) NOT NULL DEFAULT 0,
                    footer_links JSON NULL,
                    show_social_links TINYINT(1) NOT NULL DEFAULT 1,
                    show_footer_links TINYINT(1) NOT NULL DEFAULT 1,
                    show_scroll_to_top TINYINT(1) NOT NULL DEFAULT 1,
                    created_at TIMESTAMP NULL,
                    updated_at TIMESTAMP NULL
                )
            ");
            
            $this->info('Table created successfully!');
        } else {
            $this->info('Table already exists.');
        }

        // Insert default data if table is empty
        if (DB::table('footer_management')->count() === 0) {
            $this->info('Inserting default data...');
            
            DB::table('footer_management')->insert([
                'logo_text' => 'Portfolio',
                'logo_type' => 'text_with_icon',
                'logo_icon' => 'P',
                'logo_icon_type' => 'letter',
                'logo_color' => '#20B2AA',
                'description' => 'Creating exceptional digital experiences through innovative design and development solutions.',
                'copyright_text' => '© {year} {company}. All rights reserved.',
                'company_name' => 'Your Portfolio',
                'footer_links' => json_encode([
                    ['title' => 'Privacy Policy', 'url' => '#', 'enabled' => true],
                    ['title' => 'Terms of Service', 'url' => '#', 'enabled' => true],
                    ['title' => 'Cookie Policy', 'url' => '#', 'enabled' => true],
                ]),
                'show_social_links' => true,
                'show_footer_links' => true,
                'show_scroll_to_top' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            $this->info('Default data inserted successfully!');
        } else {
            $this->info('Data already exists.');
        }

        $this->info('Footer Management setup completed!');
        
        return 0;
    }
}
