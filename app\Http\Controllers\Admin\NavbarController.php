<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HeroSection;
use Illuminate\Http\Request;
use Inertia\Inertia;

class NavbarController extends Controller
{
    public function index()
    {
        $heroSection = HeroSection::getOrCreate();

        return Inertia::render('admin/navbar-settings', [
            'profile' => [
                'id' => $heroSection->id,
                'logo_text' => $heroSection->logo_text,
                'logo_type' => $heroSection->logo_type,
                'logo_icon' => $heroSection->logo_icon,
                'logo_icon_type' => $heroSection->logo_icon_type,
                'logo_image' => $heroSection->logo_image,
                'logo_color' => $heroSection->logo_color,
                'navbar_items' => $heroSection->navbar_items,
            ],
        ]);
    }

    public function updateLogo(Request $request)
    {
        $request->validate([
            'logo_text' => 'required|string|max:255',
            'logo_type' => 'required|string|in:text_only,icon_only,text_with_icon',
            'logo_icon' => 'required|string',
            'logo_icon_type' => 'required|string|in:letter,svg,image',
            'logo_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg,webp|max:2048',
            'logo_color' => 'required|string|max:255',
            'hire_me_text' => 'required|string|max:255',
            'hire_me_url' => 'required|string|max:255',
            'hire_me_enabled' => 'required|boolean',
        ]);

        $heroSection = HeroSection::getOrCreate();

        // Handle logo image upload
        $logoImagePath = $heroSection->logo_image; // Keep existing image if no new upload
        if ($request->hasFile('logo_image')) {
            // Delete old image if it exists
            if ($heroSection->logo_image && file_exists(public_path('storage/' . $heroSection->logo_image))) {
                unlink(public_path('storage/' . $heroSection->logo_image));
            }

            // Store new image
            $logoImagePath = $request->file('logo_image')->store('logos', 'public');
        }

        // Update logo settings
        $heroSection->logo_text = $request->logo_text;
        $heroSection->logo_type = $request->logo_type;
        $heroSection->logo_icon = $request->logo_icon;
        $heroSection->logo_icon_type = $request->logo_icon_type;
        $heroSection->logo_image = $logoImagePath;
        $heroSection->logo_color = $request->logo_color;

        // Update hire me button settings
        $heroSection->hire_me_text = $request->hire_me_text;
        $heroSection->hire_me_url = $request->hire_me_url;
        $heroSection->hire_me_enabled = $request->hire_me_enabled;

        $heroSection->save();

        return redirect()->back()->with('success', 'Logo settings updated successfully.');
    }

    public function updateNavbarItems(Request $request)
    {
        $request->validate([
            'navbar_items' => 'required|array',
            'navbar_items.*.title' => 'required|string|max:255',
            'navbar_items.*.href' => 'required|string|max:255',
        ]);

        $heroSection = HeroSection::getOrCreate();

        // Update navbar items
        $heroSection->navbar_items = $request->navbar_items;
        $heroSection->save();

        return redirect()->back()->with('success', 'Navbar items updated successfully.');
    }
}
