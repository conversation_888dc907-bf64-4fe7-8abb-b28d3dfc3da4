import { Head, useForm, usePage, router } from '@inertiajs/react';
import AdminLayout from '@/layouts/admin-layout';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
    Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle
} from '@/components/ui/card';
import {
    Ta<PERSON>, TabsContent, TabsList, TabsTrigger
} from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import {
    Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from '@/components/ui/select';
import { Upload, Save, User, Github, Twitter, Linkedin, FileText, Menu, X, Plus, GripVertical } from 'lucide-react';
import { toast } from 'sonner';

import { Switch } from '@/components/ui/switch';
import { DynamicLogo } from '@/components/ui/dynamic-logo';
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    DragEndEvent
} from '@dnd-kit/core';
import {
    arrayMove,
    SortableContext,
    sortableKeyboardCoordinates,
    useSortable,
    verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface NavbarItem {
    title: string;
    href: string;
    [key: string]: any;
}

interface Profile {
    id: number;
    pageTitle: string;
    title: string;
    about: string;
    yearsExperience: number;
    projectsCompleted: number;
    avatar: string | null;
    resume: string | null;
    githubUrl: string | null;
    githubEnabled: boolean;
    linkedinUrl: string | null;
    linkedinEnabled: boolean;
    twitterUrl: string | null;
    twitterEnabled: boolean;
    facebookUrl: string | null;
    facebookEnabled: boolean;
    instagramUrl: string | null;
    instagramEnabled: boolean;
    dribbbleUrl: string | null;
    dribbbleEnabled: boolean;
    behanceUrl: string | null;
    behanceEnabled: boolean;
    fiverrUrl: string | null;
    fiverrEnabled: boolean;
    upworkUrl: string | null;
    upworkEnabled: boolean;
    freelancerUrl: string | null;
    freelancerEnabled: boolean;
    peopleperhourUrl: string | null;
    peopleperhourEnabled: boolean;
    upassignUrl: string | null;
    upassignEnabled: boolean;
    isAvailable: boolean;
    ctaText: string;
    ctaSecondaryText: string;
    ctaUrl: string;
    ctaSecondaryUrl: string;
    logo_text?: string;
    logo_type?: string;
    logo_icon?: string;
    logo_icon_type?: string;
    logo_image?: string | null;
    logo_color?: string;
    navbar_items?: NavbarItem[];
    hire_me_text?: string;
    hire_me_url?: string;
    hire_me_enabled?: boolean;
}

interface FormData {
    pageTitle: string;
    title: string;
    about: string;
    yearsExperience: number;
    projectsCompleted: number;
    isAvailable: boolean;
    ctaText: string;
    ctaSecondaryText: string;
    ctaUrl: string;
    ctaSecondaryUrl: string;
    avatar: File | null;
    resume: File | null;
    githubUrl: string;
    githubEnabled: boolean;
    linkedinUrl: string;
    linkedinEnabled: boolean;
    twitterUrl: string;
    twitterEnabled: boolean;
    facebookUrl: string;
    facebookEnabled: boolean;
    instagramUrl: string;
    instagramEnabled: boolean;
    dribbbleUrl: string;
    dribbbleEnabled: boolean;
    behanceUrl: string;
    behanceEnabled: boolean;
    fiverrUrl: string;
    fiverrEnabled: boolean;
    upworkUrl: string;
    upworkEnabled: boolean;
    freelancerUrl: string;
    freelancerEnabled: boolean;
    peopleperhourUrl: string;
    peopleperhourEnabled: boolean;
    upassignUrl: string;
    upassignEnabled: boolean;
    logo_text: string;
    logo_type: string;
    logo_icon: string;
    logo_icon_type: string;
    logo_image: File | null;
    logo_color: string;
    navbar_items: NavbarItem[];
    hire_me_text: string;
    hire_me_url: string;
    hire_me_enabled: boolean;
    [key: string]: any;
}

interface Props {
    profile: Profile;
}

interface SortableItemProps {
    id: string;
    item: NavbarItem;
    index: number;
    updateNavbarItem: (index: number, field: 'title' | 'href', value: string) => void;
}

function SortableItem({ id, item, index, updateNavbarItem }: SortableItemProps) {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition
    } = useSortable({ id });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
    };

    return (
        <div
            ref={setNodeRef}
            style={style}
            className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-md"
        >
            <div
                {...attributes}
                {...listeners}
                className="cursor-grab"
            >
                <GripVertical className="w-5 h-5 text-gray-400" />
            </div>

            <div className="flex-1 grid grid-cols-2 gap-2">
                <div>
                    <Input
                        placeholder="Item Title"
                        value={item.title}
                        onChange={(e) => updateNavbarItem(index, 'title', e.target.value)}
                    />
                </div>
                <div>
                    <Input
                        placeholder="Item Link (e.g., 'home', 'services')"
                        value={item.href}
                        onChange={(e) => updateNavbarItem(index, 'href', e.target.value)}
                    />
                </div>
            </div>


        </div>
    );
}

export default function AdminProfile({ profile }: Props) {
    const [avatarPreview, setAvatarPreview] = useState<string | null>(profile.avatar);
    const [selectedImagePreview, setSelectedImagePreview] = useState<string | null>(null);
    const [showCustomColor, setShowCustomColor] = useState(false);
    const [activeTab, setActiveTab] = useState('hero');

    // Cleanup preview URL when component unmounts or preview changes
    useEffect(() => {
        return () => {
            if (selectedImagePreview && selectedImagePreview.startsWith('blob:')) {
                URL.revokeObjectURL(selectedImagePreview);
            }
        };
    }, [selectedImagePreview]);
    // Ensure navbar_items is always an array
    const getNavbarItems = () => {
        if (Array.isArray(profile.navbar_items)) {
            return profile.navbar_items;
        }
        if (typeof profile.navbar_items === 'string') {
            try {
                const parsed = JSON.parse(profile.navbar_items);
                return Array.isArray(parsed) ? parsed : [];
            } catch (e) {
                console.warn('Failed to parse navbar_items JSON:', e);
                return [];
            }
        }
        return [
            { title: 'Home', href: 'home' },
            { title: 'Services', href: 'services' },
            { title: 'Works', href: 'works' },
            { title: 'Skills', href: 'skills' },
            { title: 'Resume', href: 'resume' },
            { title: 'Testimonials', href: 'testimonials' },
            { title: 'Contact', href: 'contact' }
        ];
    };

    const [navbarItems, setNavbarItems] = useState<NavbarItem[]>(getNavbarItems());

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const { data, setData, post, processing, errors, reset } = useForm<FormData>({
        pageTitle: profile.pageTitle || '',
        title: profile.title || '',
        about: profile.about || '',
        yearsExperience: profile.yearsExperience || 0,
        projectsCompleted: profile.projectsCompleted || 0,
        isAvailable: profile.isAvailable ?? true,
        ctaText: profile.ctaText || 'View Work',
        ctaSecondaryText: profile.ctaSecondaryText || 'Download CV',
        ctaUrl: profile.ctaUrl || '#works',
        ctaSecondaryUrl: profile.ctaSecondaryUrl || '#',
        avatar: null as File | null,
        resume: null as File | null,
        githubUrl: profile.githubUrl || '',
        githubEnabled: profile.githubEnabled ?? true,
        linkedinUrl: profile.linkedinUrl || '',
        linkedinEnabled: profile.linkedinEnabled ?? true,
        twitterUrl: profile.twitterUrl || '',
        twitterEnabled: profile.twitterEnabled ?? true,
        facebookUrl: profile.facebookUrl || '',
        facebookEnabled: profile.facebookEnabled ?? true,
        instagramUrl: profile.instagramUrl || '',
        instagramEnabled: profile.instagramEnabled ?? true,
        dribbbleUrl: profile.dribbbleUrl || '',
        dribbbleEnabled: profile.dribbbleEnabled ?? true,
        behanceUrl: profile.behanceUrl || '',
        behanceEnabled: profile.behanceEnabled ?? true,
        fiverrUrl: profile.fiverrUrl || '',
        fiverrEnabled: profile.fiverrEnabled ?? true,
        upworkUrl: profile.upworkUrl || '',
        upworkEnabled: profile.upworkEnabled ?? true,
        freelancerUrl: profile.freelancerUrl || '',
        freelancerEnabled: profile.freelancerEnabled ?? true,
        peopleperhourUrl: profile.peopleperhourUrl || '',
        peopleperhourEnabled: profile.peopleperhourEnabled ?? true,
        upassignUrl: profile.upassignUrl || '',
        upassignEnabled: profile.upassignEnabled ?? true,
        logo_text: profile.logo_text || 'Portfolio',
        logo_type: profile.logo_type || 'text_with_icon',
        logo_icon: profile.logo_icon || 'P',
        logo_icon_type: profile.logo_icon_type || 'letter',
        logo_image: null,
        logo_color: profile.logo_color || '#1f2937',
        navbar_items: navbarItems,
        hire_me_text: profile.hire_me_text || 'Hire Me',
        hire_me_url: profile.hire_me_url || '#contact',
        hire_me_enabled: profile.hire_me_enabled ?? true,
    });



    // Update form data when navbarItems change
    useEffect(() => {
        setData('navbar_items', navbarItems);
    }, [navbarItems]);

    // Check if current logo color is a predefined color or custom
    useEffect(() => {
        const predefinedColors = [
            '#1f2937', '#000000', '#374151', '#4B5563', '#ffffff',
            '#3b82f6', '#10b981', '#8b5cf6', '#ef4444', '#f59e0b', '#20B2AA'
        ];

        if (data.logo_color && !predefinedColors.includes(data.logo_color)) {
            setShowCustomColor(true);
        }
    }, [data.logo_color]);

    // Note: Removed auto-disable useEffect hooks to prevent circular dependency
    // Users should be able to enable switches and then enter URLs

    // Helper function to count currently enabled social media platforms
    const getEnabledPlatformsCount = () => {
        const platforms = [
            data.githubEnabled,
            data.twitterEnabled,
            data.linkedinEnabled,
            data.facebookEnabled,
            data.instagramEnabled,
            data.dribbbleEnabled,
            data.behanceEnabled,
            data.upassignEnabled,
            data.fiverrEnabled,
            data.upworkEnabled,
            data.freelancerEnabled,
            data.peopleperhourEnabled,
        ];
        return platforms.filter(Boolean).length;
    };

    // Function to handle switch changes with enhanced validation and popup messages
    const handleSwitchChange = (platform: string, checked: boolean) => {
        const currentEnabledCount = getEnabledPlatformsCount();

        if (checked) {
            // Trying to enable a platform
            if (currentEnabledCount >= 3) {
                // Show error popup when trying to enable 4th platform
                toast.error('Maximum 3 social media platforms allowed', {
                    description: 'You can only enable up to 3 social media platforms. Please disable one first.',
                    duration: 4000,
                });
                return; // Don't allow enabling
            } else {
                // Successfully enabled - show success popup
                const platformName = getPlatformDisplayName(platform);
                toast.success(`${platformName} enabled successfully`, {
                    description: `${platformName} social media link has been enabled.`,
                    duration: 3000,
                });
                setData(platform as keyof FormData, checked);
            }
        } else {
            // Disabling a platform - show info popup
            const platformName = getPlatformDisplayName(platform);
            toast.info(`${platformName} disabled`, {
                description: `${platformName} social media link has been disabled.`,
                duration: 3000,
            });
            setData(platform as keyof FormData, checked);
        }
    };

    // Helper function to get display name for platforms
    const getPlatformDisplayName = (platform: string): string => {
        const platformNames: { [key: string]: string } = {
            'githubEnabled': 'GitHub',
            'twitterEnabled': 'Twitter',
            'linkedinEnabled': 'LinkedIn',
            'facebookEnabled': 'Facebook',
            'instagramEnabled': 'Instagram',
            'dribbbleEnabled': 'Dribbble',
            'behanceEnabled': 'Behance',
            'upassignEnabled': 'Upassign',
            'fiverrEnabled': 'Fiverr',
            'upworkEnabled': 'Upwork',
            'freelancerEnabled': 'Freelancer',
            'peopleperhourEnabled': 'PeoplePerHour',
        };
        return platformNames[platform] || platform.replace('Enabled', '');
    };

    // Helper function to check if a switch should be disabled
    const isSwitchDisabled = (platform: string): boolean => {
        const currentEnabledCount = getEnabledPlatformsCount();
        const isCurrentPlatformEnabled = data[platform as keyof FormData] as boolean;

        // If current platform is enabled, it should not be disabled
        if (isCurrentPlatformEnabled) {
            return false;
        }

        // If we already have 3 enabled platforms, disable all other switches
        return currentEnabledCount >= 3;
    };

    // Handle form submission for hero section
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Use Inertia to submit the form with current data
        post(route('admin.profile.hero.update'), {
            onSuccess: () => {
                const availabilityStatus = data.isAvailable ? 'enabled' : 'disabled';
                toast.success('Profile updated successfully', {
                    description: `Availability status is now ${availabilityStatus}. Homepage will reflect the changes.`,
                    duration: 4000,
                });
                // Auto-reload page to show updated data
                setTimeout(() => {
                    router.reload();
                }, 500);
            },
            onError: (errors) => {
                console.error('Profile update errors:', errors);
                toast.error('Failed to update profile. Please check the form for errors.');
            },
        });
    };

    // Handle social links submission
    const handleSocialSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Use Inertia to submit the form with current data
        post(route('admin.profile.social.update'), {
            onSuccess: () => {
                toast.success('Social links updated successfully');
                // Auto-reload page to show updated data
                setTimeout(() => {
                    router.reload();
                }, 500);
            },
            onError: (errors) => {
                console.error('Social links update errors:', errors);
                toast.error('Failed to update social links. Please check the form for errors.');
            },
        });
    };

    // Handle form submission for logo settings
    const handleLogoSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.navbar.logo.update'), {
            onSuccess: () => {
                toast.success('Logo settings updated successfully');
                // Clear the preview since the image is now saved
                if (selectedImagePreview && selectedImagePreview.startsWith('blob:')) {
                    URL.revokeObjectURL(selectedImagePreview);
                }
                setSelectedImagePreview(null);
                // Auto-reload page to show updated data
                setTimeout(() => {
                    router.reload();
                }, 500);
            },
            onError: (errors) => {
                console.error('Logo update errors:', errors);
                toast.error('Failed to update logo settings. Please check the form for errors.');
            },
        });
    };

    // Handle form submission for navbar items
    const handleNavbarSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.navbar.items.update'), {
            onSuccess: () => {
                toast.success('Navbar items updated successfully');
                // Auto-reload page to show updated data
                setTimeout(() => {
                    router.reload();
                }, 500);
            },
            onError: (errors) => {
                console.error('Navbar update errors:', errors);
                toast.error('Failed to update navbar items. Please check the form for errors.');
            },
        });
    };

    // Handle form submission for hire me button
    const handleHireMeSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.navbar.logo.update'), {
            onSuccess: () => {
                toast.success('Hire Me button settings updated successfully');
                // Auto-reload page to show updated data
                setTimeout(() => {
                    router.reload();
                }, 500);
            },
            onError: (errors) => {
                console.error('Hire Me button update errors:', errors);
                toast.error('Failed to update hire me button settings. Please check the form for errors.');
            },
        });
    };



    // Update a navbar item
    const updateNavbarItem = (index: number, field: 'title' | 'href', value: string) => {
        const updatedItems = [...navbarItems];
        updatedItems[index] = { ...updatedItems[index], [field]: value };
        setNavbarItems(updatedItems);
    };

    // Handle drag and drop reordering
    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;

        if (over && active.id !== over.id) {
            setNavbarItems((items) => {
                const oldIndex = parseInt(active.id.toString().split('-')[1]);
                const newIndex = parseInt(over.id.toString().split('-')[1]);

                return arrayMove(items, oldIndex, newIndex);
            });
        }
    };

    // Handle avatar upload
    const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setData('avatar', file);

            // Create a preview URL
            const reader = new FileReader();
            reader.onload = (e) => {
                if (e.target?.result) {
                    setAvatarPreview(e.target.result as string);
                }
            };
            reader.readAsDataURL(file);
        }
    };

    // Handle resume upload
    const handleResumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setData('resume', file);
        }
    };

    // Handle resume download
    const handleResumeDownload = () => {
        if (profile.resume) {
            window.open('/download-hero-resume', '_blank');
        }
    };

    // Handle resume delete
    const handleResumeDelete = async () => {
        if (confirm('Are you sure you want to delete the current resume?')) {
            try {
                const response = await fetch('/admin/profile/delete-resume', {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    toast.success('Resume deleted successfully');
                    // Refresh the page to update the UI
                    router.reload();
                } else {
                    toast.error('Failed to delete resume');
                }
            } catch (error) {
                toast.error('Failed to delete resume');
                console.error('Error deleting resume:', error);
            }
        }
    };

    return (
        <AdminLayout>
            <Head title="Profile | Admin Dashboard" />

            <div className="container p-6">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">Profile Settings</h1>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="outline"
                            onClick={() => window.open(route('welcome'), '_blank')}
                        >
                            View Portfolio
                        </Button>
                    </div>
                </div>



                <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="mb-6">
                        <TabsTrigger value="hero">Profile & Hero Section</TabsTrigger>
                        <TabsTrigger value="social">Social Links</TabsTrigger>
                        <TabsTrigger value="navbar">Navbar Settings</TabsTrigger>
                    </TabsList>

                    {/* Hero Section Tab */}
                    <TabsContent value="hero">
                        <Card>
                            <CardHeader>
                                <CardTitle>Profile & Hero Section</CardTitle>
                                <CardDescription>
                                    Update your profile information and the content displayed in the hero section of your portfolio.
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div className="col-span-1">
                                            <div className="flex flex-col items-center justify-center p-4 border-2 border-dashed border-gray-200 rounded-lg">
                                                <div className="relative w-40 h-40 overflow-hidden rounded-lg mb-4 bg-gray-100">
                                                    {avatarPreview ? (
                                                        <img
                                                            src={avatarPreview}
                                                            alt="Profile"
                                                            className="w-full h-full object-cover"
                                                        />
                                                    ) : (
                                                        <div className="flex items-center justify-center w-full h-full bg-gray-200 text-gray-500">
                                                            <User size={60} />
                                                        </div>
                                                    )}
                                                </div>
                                                <Label htmlFor="avatar" className="cursor-pointer">
                                                    <div className="flex items-center justify-center px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50">
                                                        <Upload className="w-4 h-4 mr-2" />
                                                        Upload Image
                                                    </div>
                                                    <input
                                                        id="avatar"
                                                        name="avatar"
                                                        type="file"
                                                        className="sr-only"
                                                        onChange={handleAvatarChange}
                                                    />
                                                </Label>
                                                <p className="mt-2 text-xs text-gray-500">
                                                    PNG, JPG or GIF. Max 2MB.
                                                </p>
                                                {errors.avatar && (
                                                    <p className="mt-1 text-sm text-red-600">{errors.avatar}</p>
                                                )}
                                            </div>

                                            <div className="mt-6">
                                                <div className="flex items-center space-x-2">
                                                    <Switch
                                                        id="isAvailable"
                                                        checked={data.isAvailable}
                                                        onCheckedChange={(checked) => {
                                                            setData('isAvailable', checked);
                                                            // Show immediate feedback
                                                            if (checked) {
                                                                toast.success('Available for work enabled', {
                                                                    description: 'The "Available for work" badge will be shown on your homepage.',
                                                                    duration: 3000,
                                                                });
                                                            } else {
                                                                toast.info('Available for work disabled', {
                                                                    description: 'The "Available for work" badge will be hidden on your homepage.',
                                                                    duration: 3000,
                                                                });
                                                            }
                                                        }}
                                                    />
                                                    <Label htmlFor="isAvailable">Available for work</Label>
                                                    <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                                                        data.isAvailable
                                                            ? 'bg-green-100 text-green-800'
                                                            : 'bg-gray-100 text-gray-800'
                                                    }`}>
                                                        {data.isAvailable ? 'Enabled' : 'Disabled'}
                                                    </span>
                                                </div>
                                                <p className="text-sm text-gray-500 mt-1">
                                                    This will show/hide the "Available for work" badge on your homepage. Changes will be saved when you submit the form.
                                                </p>
                                            </div>

                                            <div className="mt-6">
                                                <Label htmlFor="resume">Resume/CV</Label>

                                                {/* Current Resume Status */}
                                                {profile.resume ? (
                                                    <div className="mt-2 p-4 bg-green-50 border border-green-200 rounded-lg">
                                                        <div className="flex items-center justify-between">
                                                            <div className="flex items-center gap-3">
                                                                <FileText className="w-5 h-5 text-green-600" />
                                                                <div>
                                                                    <p className="text-sm font-medium text-green-900">Resume file uploaded</p>
                                                                    <p className="text-xs text-green-700">File is ready for download</p>
                                                                </div>
                                                            </div>
                                                            <div className="flex gap-2">
                                                                <Button
                                                                    type="button"
                                                                    onClick={handleResumeDownload}
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="text-green-700 border-green-300 hover:bg-green-50"
                                                                >
                                                                    <FileText className="w-4 h-4 mr-1" />
                                                                    Download
                                                                </Button>
                                                                <Button
                                                                    type="button"
                                                                    onClick={handleResumeDelete}
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="text-red-700 border-red-300 hover:bg-red-50"
                                                                >
                                                                    <X className="w-4 h-4 mr-1" />
                                                                    Delete
                                                                </Button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ) : null}

                                                {/* Upload New Resume */}
                                                <div className="mt-2">
                                                    <Label htmlFor="resume" className="cursor-pointer">
                                                        <div className="flex items-center justify-center px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50">
                                                            <Upload className="w-4 h-4 mr-2" />
                                                            {profile.resume ? 'Replace Resume' : 'Upload Resume'}
                                                        </div>
                                                        <input
                                                            id="resume"
                                                            name="resume"
                                                            type="file"
                                                            accept=".pdf,.doc,.docx"
                                                            className="sr-only"
                                                            onChange={handleResumeChange}
                                                        />
                                                    </Label>
                                                    {data.resume && (
                                                        <span className="ml-2 text-sm text-gray-500">
                                                            New file selected: {data.resume.name}
                                                        </span>
                                                    )}
                                                </div>
                                                <p className="mt-1 text-xs text-gray-500">
                                                    PDF, DOC, or DOCX. Max 10MB.
                                                </p>
                                            </div>
                                        </div>

                                        <div className="col-span-2 space-y-4">
                                            <div>
                                                <Label htmlFor="pageTitle">Page Title</Label>
                                                <Input
                                                    id="pageTitle"
                                                    value={data.pageTitle}
                                                    onChange={(e) => setData('pageTitle', e.target.value)}
                                                    placeholder="e.g., John Doe - Portfolio, Creative Designer & Developer"
                                                    className="mt-1"
                                                />
                                                {errors.pageTitle && (
                                                    <p className="mt-1 text-sm text-red-600">{errors.pageTitle}</p>
                                                )}
                                            </div>

                                            <div>
                                                <Label htmlFor="title">Professional Title</Label>
                                                <Input
                                                    id="title"
                                                    value={data.title}
                                                    onChange={(e) => setData('title', e.target.value)}
                                                    placeholder="e.g. Creative Designer & Developer"
                                                    className="mt-1"
                                                />
                                                {errors.title && (
                                                    <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                                                )}
                                                <p className="mt-1 text-xs text-gray-500">
                                                    The main heading on your homepage. The words "Designer", "Developer", etc. will be highlighted.
                                                </p>
                                            </div>

                                            <div>
                                                <Label htmlFor="about">About / Tagline</Label>
                                                <Textarea
                                                    id="about"
                                                    value={data.about}
                                                    onChange={(e) => setData('about', e.target.value)}
                                                    placeholder="Description of what you do that will appear on your homepage"
                                                    className="mt-1 min-h-[120px]"
                                                />
                                                {errors.about && (
                                                    <p className="mt-1 text-sm text-red-600">{errors.about}</p>
                                                )}
                                                <p className="mt-1 text-xs text-gray-500">
                                                    This text appears on your homepage and helps visitors understand what you do.
                                                </p>
                                            </div>

                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <Label htmlFor="yearsExperience">Years of Experience</Label>
                                                    <Input
                                                        id="yearsExperience"
                                                        type="text"
                                                        value={data.yearsExperience.toString()}
                                                        onChange={(e) => {
                                                            const value = e.target.value;
                                                            // Only allow numbers
                                                            if (value === '' || /^\d+$/.test(value)) {
                                                                setData('yearsExperience', value === '' ? 0 : parseInt(value));
                                                            }
                                                        }}
                                                        className="mt-1 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                                        placeholder="Enter years of experience"
                                                    />
                                                    {errors.yearsExperience && (
                                                        <p className="mt-1 text-sm text-red-600">{errors.yearsExperience}</p>
                                                    )}
                                                </div>

                                                <div>
                                                    <Label htmlFor="projectsCompleted">Projects Completed</Label>
                                                    <Input
                                                        id="projectsCompleted"
                                                        type="text"
                                                        value={data.projectsCompleted.toString()}
                                                        onChange={(e) => {
                                                            const value = e.target.value;
                                                            // Only allow numbers
                                                            if (value === '' || /^\d+$/.test(value)) {
                                                                setData('projectsCompleted', value === '' ? 0 : parseInt(value));
                                                            }
                                                        }}
                                                        className="mt-1 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                                        placeholder="Enter projects completed"
                                                    />
                                                    {errors.projectsCompleted && (
                                                        <p className="mt-1 text-sm text-red-600">{errors.projectsCompleted}</p>
                                                    )}
                                                </div>
                                            </div>

                                            <div className="border-t border-gray-200 pt-4 mt-4">
                                                <h3 className="text-lg font-medium mb-3">Call-to-Action Buttons</h3>

                                                <div className="grid grid-cols-2 gap-4">
                                                    <div>
                                                        <Label htmlFor="ctaText">Primary Button Text</Label>
                                                        <Input
                                                            id="ctaText"
                                                            value={data.ctaText}
                                                            onChange={(e) => setData('ctaText', e.target.value)}
                                                            className="mt-1"
                                                        />
                                                        {errors.ctaText && (
                                                            <p className="mt-1 text-sm text-red-600">{errors.ctaText}</p>
                                                        )}
                                                    </div>

                                                    <div>
                                                        <Label htmlFor="ctaUrl">Primary Button URL</Label>
                                                        <Input
                                                            id="ctaUrl"
                                                            value={data.ctaUrl}
                                                            onChange={(e) => setData('ctaUrl', e.target.value)}
                                                            className="mt-1"
                                                            placeholder="#works or https://..."
                                                        />
                                                        {errors.ctaUrl && (
                                                            <p className="mt-1 text-sm text-red-600">{errors.ctaUrl}</p>
                                                        )}
                                                    </div>

                                                    <div>
                                                        <Label htmlFor="ctaSecondaryText">Secondary Button Text</Label>
                                                        <Input
                                                            id="ctaSecondaryText"
                                                            value={data.ctaSecondaryText}
                                                            onChange={(e) => setData('ctaSecondaryText', e.target.value)}
                                                            className="mt-1"
                                                        />
                                                        {errors.ctaSecondaryText && (
                                                            <p className="mt-1 text-sm text-red-600">{errors.ctaSecondaryText}</p>
                                                        )}
                                                    </div>

                                                    <div>
                                                        <Label htmlFor="ctaSecondaryUrl">Secondary Button URL</Label>
                                                        <Input
                                                            id="ctaSecondaryUrl"
                                                            value={data.ctaSecondaryUrl}
                                                            onChange={(e) => setData('ctaSecondaryUrl', e.target.value)}
                                                            className="mt-1"
                                                            placeholder="#download or https://..."
                                                        />
                                                        {errors.ctaSecondaryUrl && (
                                                            <p className="mt-1 text-sm text-red-600">{errors.ctaSecondaryUrl}</p>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>


                                        </div>
                                    </div>

                                    <div className="flex justify-end">
                                        <Button type="submit" className="bg-[#20B2AA] hover:bg-[#1a9994]" disabled={processing}>
                                            {processing ? 'Saving...' : 'Save Profile'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Social Links Tab */}
                    <TabsContent value="social">
                        <Card>
                            <CardHeader>
                                <CardTitle>Social Media Links</CardTitle>
                                <CardDescription>
                                    Add or update your social media profiles. You can enable up to 3 platforms at once. When 3 platforms are enabled, other toggles will be automatically disabled. You'll see popup notifications for all enable/disable actions.
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSocialSubmit} className="space-y-6">
                                    <div className="space-y-4">
                                        <div>
                                            <div className="flex items-center justify-between mb-2">
                                                <Label htmlFor="githubUrl">GitHub</Label>
                                                <div className="flex items-center space-x-2">
                                                    <Switch
                                                        id="githubEnabled"
                                                        checked={data.githubEnabled}
                                                        disabled={isSwitchDisabled('githubEnabled')}
                                                        onCheckedChange={(checked) => handleSwitchChange('githubEnabled', checked)}
                                                    />
                                                    <Label htmlFor="githubEnabled" className="text-sm text-gray-600">
                                                        {data.githubEnabled ? 'Enabled' : 'Disabled'}
                                                    </Label>
                                                    {data.githubEnabled && !data.githubUrl?.trim() && (
                                                        <span className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                                            URL needed
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="relative mt-1">
                                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <Github className="h-4 w-4 text-gray-400" />
                                                </div>
                                                <Input
                                                    id="githubUrl"
                                                    type="url"
                                                    value={data.githubUrl || ''}
                                                    onChange={(e) => setData('githubUrl', e.target.value)}
                                                    className="pl-10"
                                                    placeholder="https://github.com/yourusername"
                                                    disabled={!data.githubEnabled}
                                                />
                                            </div>
                                            {errors.githubUrl && (
                                                <p className="mt-1 text-sm text-red-600">{errors.githubUrl}</p>
                                            )}
                                        </div>

                                        <div>
                                            <div className="flex items-center justify-between mb-2">
                                                <Label htmlFor="twitterUrl">Twitter</Label>
                                                <div className="flex items-center space-x-2">
                                                    <Switch
                                                        id="twitterEnabled"
                                                        checked={data.twitterEnabled}
                                                        disabled={isSwitchDisabled('twitterEnabled')}
                                                        onCheckedChange={(checked) => handleSwitchChange('twitterEnabled', checked)}
                                                    />
                                                    <Label htmlFor="twitterEnabled" className="text-sm text-gray-600">
                                                        {data.twitterEnabled ? 'Enabled' : 'Disabled'}
                                                    </Label>
                                                    {data.twitterEnabled && !data.twitterUrl?.trim() && (
                                                        <span className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                                            URL needed
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="relative mt-1">
                                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <Twitter className="h-4 w-4 text-gray-400" />
                                                </div>
                                                <Input
                                                    id="twitterUrl"
                                                    type="url"
                                                    value={data.twitterUrl || ''}
                                                    onChange={(e) => setData('twitterUrl', e.target.value)}
                                                    className="pl-10"
                                                    placeholder="https://twitter.com/yourusername"
                                                    disabled={!data.twitterEnabled}
                                                />
                                            </div>
                                            {errors.twitterUrl && (
                                                <p className="mt-1 text-sm text-red-600">{errors.twitterUrl}</p>
                                            )}
                                        </div>

                                        <div>
                                            <div className="flex items-center justify-between mb-2">
                                                <Label htmlFor="linkedinUrl">LinkedIn</Label>
                                                <div className="flex items-center space-x-2">
                                                    <Switch
                                                        id="linkedinEnabled"
                                                        checked={data.linkedinEnabled}
                                                        disabled={isSwitchDisabled('linkedinEnabled')}
                                                        onCheckedChange={(checked) => handleSwitchChange('linkedinEnabled', checked)}
                                                    />
                                                    <Label htmlFor="linkedinEnabled" className="text-sm text-gray-600">
                                                        {data.linkedinEnabled ? 'Enabled' : 'Disabled'}
                                                    </Label>
                                                    {data.linkedinEnabled && !data.linkedinUrl?.trim() && (
                                                        <span className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                                            URL needed
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="relative mt-1">
                                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <Linkedin className="h-4 w-4 text-gray-400" />
                                                </div>
                                                <Input
                                                    id="linkedinUrl"
                                                    type="url"
                                                    value={data.linkedinUrl || ''}
                                                    onChange={(e) => setData('linkedinUrl', e.target.value)}
                                                    className="pl-10"
                                                    placeholder="https://linkedin.com/in/yourusername"
                                                    disabled={!data.linkedinEnabled}
                                                />
                                            </div>
                                            {errors.linkedinUrl && (
                                                <p className="mt-1 text-sm text-red-600">{errors.linkedinUrl}</p>
                                            )}
                                        </div>
                                    </div>

                                    {/* Facebook */}
                                    <div>
                                        <div className="flex items-center justify-between mb-2">
                                            <Label htmlFor="facebookUrl">Facebook</Label>
                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    id="facebookEnabled"
                                                    checked={data.facebookEnabled}
                                                    disabled={isSwitchDisabled('facebookEnabled')}
                                                    onCheckedChange={(checked) => handleSwitchChange('facebookEnabled', checked)}
                                                />
                                                <Label htmlFor="facebookEnabled" className="text-sm text-gray-600">
                                                    {data.facebookEnabled ? 'Enabled' : 'Disabled'}
                                                </Label>
                                                {data.facebookEnabled && !data.facebookUrl?.trim() && (
                                                    <span className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                                        URL needed
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                        <div className="relative mt-1">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg className="h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                                </svg>
                                            </div>
                                            <Input
                                                id="facebookUrl"
                                                value={data.facebookUrl as string}
                                                onChange={(e) => setData('facebookUrl', e.target.value)}
                                                className="pl-10"
                                                placeholder="https://facebook.com/yourusername"
                                                disabled={!data.facebookEnabled}
                                            />
                                        </div>
                                        {errors.facebookUrl && (
                                            <p className="mt-1 text-sm text-red-600">{errors.facebookUrl}</p>
                                        )}
                                    </div>

                                    {/* Instagram */}
                                    <div>
                                        <div className="flex items-center justify-between mb-2">
                                            <Label htmlFor="instagramUrl">Instagram</Label>
                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    id="instagramEnabled"
                                                    checked={data.instagramEnabled}
                                                    disabled={isSwitchDisabled('instagramEnabled')}
                                                    onCheckedChange={(checked) => handleSwitchChange('instagramEnabled', checked)}
                                                />
                                                <Label htmlFor="instagramEnabled" className="text-sm text-gray-600">
                                                    {data.instagramEnabled ? 'Enabled' : 'Disabled'}
                                                </Label>
                                                {data.instagramEnabled && !data.instagramUrl?.trim() && (
                                                    <span className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                                        URL needed
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                        <div className="relative mt-1">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg className="h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                                </svg>
                                            </div>
                                            <Input
                                                id="instagramUrl"
                                                value={data.instagramUrl as string}
                                                onChange={(e) => setData('instagramUrl', e.target.value)}
                                                className="pl-10"
                                                placeholder="https://instagram.com/yourusername"
                                                disabled={!data.instagramEnabled}
                                            />
                                        </div>
                                        {errors.instagramUrl && (
                                            <p className="mt-1 text-sm text-red-600">{errors.instagramUrl}</p>
                                        )}
                                    </div>

                                    {/* Dribbble */}
                                    <div>
                                        <div className="flex items-center justify-between mb-2">
                                            <Label htmlFor="dribbbleUrl">Dribbble</Label>
                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    id="dribbbleEnabled"
                                                    checked={data.dribbbleEnabled}
                                                    disabled={isSwitchDisabled('dribbbleEnabled')}
                                                    onCheckedChange={(checked) => handleSwitchChange('dribbbleEnabled', checked)}
                                                />
                                                <Label htmlFor="dribbbleEnabled" className="text-sm text-gray-600">
                                                    {data.dribbbleEnabled ? 'Enabled' : 'Disabled'}
                                                </Label>
                                                {data.dribbbleEnabled && !data.dribbbleUrl?.trim() && (
                                                    <span className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                                        URL needed
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                        <div className="relative mt-1">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg className="h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M12 24C5.385 24 0 18.615 0 12S5.385 0 12 0s12 5.385 12 12-5.385 12-12 12zm10.12-10.358c-.35-.11-3.17-.953-6.384-.438 1.34 3.684 1.887 6.684 1.992 7.308 2.3-1.555 3.936-4.02 4.395-6.87zm-6.115 7.808c-.153-.9-.75-4.032-2.19-7.77l-.066.02c-5.79 2.015-7.86 6.025-8.04 6.4 1.73 1.358 3.92 2.166 6.29 2.166 1.42 0 2.77-.29 4-.816zm-11.62-2.58c.232-.4 3.045-5.055 8.332-6.765.135-.045.27-.084.405-.12-.26-.585-.54-1.167-.832-1.74C7.17 11.775 2.206 11.71 1.756 11.7l-.004.312c0 2.633.998 5.037 2.634 6.855zm-2.42-8.955c.46.008 4.683.026 9.477-1.248-1.698-3.018-3.53-5.558-3.8-5.928-2.868 1.35-5.01 3.99-5.676 7.17zM9.6 2.052c.282.38 2.145 2.914 3.822 6 3.645-1.365 5.19-3.44 5.373-3.702-1.81-1.61-4.19-2.586-6.795-2.586-.825 0-1.63.1-2.4.285zm10.335 3.483c-.218.29-1.935 2.493-5.724 4.04.24.49.47.985.68 1.486.08.18.15.36.22.53 3.41-.43 6.8.26 7.14.33-.02-2.42-.88-4.64-2.31-6.38z"/>
                                                </svg>
                                            </div>
                                            <Input
                                                id="dribbbleUrl"
                                                value={data.dribbbleUrl as string}
                                                onChange={(e) => setData('dribbbleUrl', e.target.value)}
                                                className="pl-10"
                                                placeholder="https://dribbble.com/yourusername"
                                                disabled={!data.dribbbleEnabled}
                                            />
                                        </div>
                                        {errors.dribbbleUrl && (
                                            <p className="mt-1 text-sm text-red-600">{errors.dribbbleUrl}</p>
                                        )}
                                    </div>

                                    {/* Behance */}
                                    <div>
                                        <div className="flex items-center justify-between mb-2">
                                            <Label htmlFor="behanceUrl">Behance</Label>
                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    id="behanceEnabled"
                                                    checked={data.behanceEnabled}
                                                    disabled={isSwitchDisabled('behanceEnabled')}
                                                    onCheckedChange={(checked) => handleSwitchChange('behanceEnabled', checked)}
                                                />
                                                <Label htmlFor="behanceEnabled" className="text-sm text-gray-600">
                                                    {data.behanceEnabled ? 'Enabled' : 'Disabled'}
                                                </Label>
                                                {data.behanceEnabled && !data.behanceUrl?.trim() && (
                                                    <span className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                                        URL needed
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                        <div className="relative mt-1">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg className="h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M6.938 4.503c.702 0 1.34.06 1.92.188.577.13 1.07.33 1.485.61.41.28.733.65.96 1.12.225.47.34 1.05.34 1.73 0 .74-.17 1.36-.507 1.86-.338.5-.837.9-1.502 1.22.906.26 1.576.72 2.022 1.37.448.66.665 1.45.665 2.36 0 .75-.13 1.39-.41 1.93-.28.55-.67 1-.17 1.35-.5.35-1.1.6-1.8.76-.69.16-1.45.24-2.28.24H0V4.51h6.938v-.007zM3.495 8.21h2.881c.795 0 1.393-.155 1.795-.465.402-.31.603-.8.603-1.46 0-.43-.083-.78-.25-1.05-.166-.27-.402-.48-.708-.63-.305-.15-.67-.26-1.093-.33-.424-.07-.896-.105-1.416-.105H3.495v4.04zm0 7.32h3.42c.483 0 .928-.05 1.34-.15.41-.1.763-.25 1.058-.45.295-.2.525-.46.69-.78.165-.32.248-.71.248-1.15 0-.48-.073-.89-.22-1.23-.147-.34-.353-.62-.618-.84-.266-.22-.58-.38-.944-.48-.363-.1-.77-.15-1.217-.15H3.495v5.23zm14.776-8.68c.928 0 1.763.156 2.507.47.744.313 1.375.75 1.893 1.312.518.563.918 1.23 1.2 2.003.282.773.423 1.62.423 2.542 0 .135-.007.27-.02.405-.014.135-.035.27-.063.405H16.99c.07.943.35 1.663.84 2.16.49.497 1.17.745 2.04.745.563 0 1.058-.11 1.485-.33.427-.22.77-.5 1.028-.84h2.566c-.49.943-1.185 1.688-2.085 2.235-.9.547-2.015.82-3.345.82-.773 0-1.478-.12-2.115-.36-.637-.24-1.186-.59-1.647-1.05-.46-.46-.816-1.02-1.068-1.68-.252-.66-.378-1.41-.378-2.25 0-.85.126-1.615.378-2.295.252-.68.608-1.26 1.068-1.725.46-.465 1.01-.825 1.647-1.065.637-.24 1.342-.36 2.115-.36zm2.37 4.995c-.07-.838-.35-1.493-.84-1.965-.49-.472-1.12-.708-1.89-.708-.42 0-.798.08-1.133.24-.335.16-.622.38-.861.66-.24.28-.426.61-.558.99-.132.38-.218.79-.258 1.23h5.54v.553zm-8.24-5.39v1.575h4.305v-1.575H12.4z"/>
                                                </svg>
                                            </div>
                                            <Input
                                                id="behanceUrl"
                                                value={data.behanceUrl as string}
                                                onChange={(e) => setData('behanceUrl', e.target.value)}
                                                className="pl-10"
                                                placeholder="https://behance.net/yourusername"
                                                disabled={!data.behanceEnabled}
                                            />
                                        </div>
                                        {errors.behanceUrl && (
                                            <p className="mt-1 text-sm text-red-600">{errors.behanceUrl}</p>
                                        )}
                                    </div>
                                    {/* Upassign */}
                                    <div>
                                        <div className="flex items-center justify-between mb-2">
                                            <Label htmlFor="upassignUrl">Upassign</Label>
                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    id="upassignEnabled"
                                                    checked={data.upassignEnabled}
                                                    disabled={isSwitchDisabled('upassignEnabled')}
                                                    onCheckedChange={(checked) => handleSwitchChange('upassignEnabled', checked)}
                                                />
                                                <Label htmlFor="upassignEnabled" className="text-sm text-gray-600">
                                                    {data.upassignEnabled ? 'Enabled' : 'Disabled'}
                                                </Label>
                                                {data.upassignEnabled && !data.upassignUrl?.trim() && (
                                                    <span className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                                        URL needed
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                        <div className="relative mt-1">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg className="h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8zm-1-13h2v2h-2V7zm0 4h2v6h-2v-6z"/>
                                                </svg>
                                            </div>
                                            <Input
                                                id="upassignUrl"
                                                value={data.upassignUrl as string}
                                                onChange={(e) => setData('upassignUrl', e.target.value)}
                                                className="pl-10"
                                                placeholder="https://upassign.com/yourusername"
                                                disabled={!data.upassignEnabled}
                                            />
                                        </div>
                                        {errors.upassignUrl && (
                                            <p className="mt-1 text-sm text-red-600">{errors.upassignUrl}</p>
                                        )}
                                    </div>

                                    {/* Fiverr */}
                                    <div>
                                        <div className="flex items-center justify-between mb-2">
                                            <Label htmlFor="fiverrUrl">Fiverr</Label>
                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    id="fiverrEnabled"
                                                    checked={data.fiverrEnabled}
                                                    disabled={isSwitchDisabled('fiverrEnabled')}
                                                    onCheckedChange={(checked) => handleSwitchChange('fiverrEnabled', checked)}
                                                />
                                                <Label htmlFor="fiverrEnabled" className="text-sm text-gray-600">
                                                    {data.fiverrEnabled ? 'Enabled' : 'Disabled'}
                                                </Label>
                                                {data.fiverrEnabled && !data.fiverrUrl?.trim() && (
                                                    <span className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                                        URL needed
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                        <div className="relative mt-1">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg className="h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M12.036 0C5.388 0 0 5.388 0 12.036s5.388 12.036 12.036 12.036 12.036-5.388 12.036-12.036S18.684 0 12.036 0zm5.4 17.1h-2.16v-1.44c-.72.96-1.68 1.68-3.24 1.68-2.4 0-3.96-1.8-3.96-4.32V8.1h2.16v4.56c0 1.44.72 2.4 2.16 2.4 1.44 0 2.88-1.2 2.88-3.12V8.1h2.16v9zm-10.8-9h2.16v9H6.636v-9z"/>
                                                </svg>
                                            </div>
                                            <Input
                                                id="fiverrUrl"
                                                value={data.fiverrUrl as string}
                                                onChange={(e) => setData('fiverrUrl', e.target.value)}
                                                className="pl-10"
                                                placeholder="https://fiverr.com/yourusername"
                                                disabled={!data.fiverrEnabled}
                                            />
                                        </div>
                                        {errors.fiverrUrl && (
                                            <p className="mt-1 text-sm text-red-600">{errors.fiverrUrl}</p>
                                        )}
                                    </div>

                                    {/* Upwork */}
                                    <div>
                                        <div className="flex items-center justify-between mb-2">
                                            <Label htmlFor="upworkUrl">Upwork</Label>
                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    id="upworkEnabled"
                                                    checked={data.upworkEnabled}
                                                    disabled={isSwitchDisabled('upworkEnabled')}
                                                    onCheckedChange={(checked) => handleSwitchChange('upworkEnabled', checked)}
                                                />
                                                <Label htmlFor="upworkEnabled" className="text-sm text-gray-600">
                                                    {data.upworkEnabled ? 'Enabled' : 'Disabled'}
                                                </Label>
                                                {data.upworkEnabled && !data.upworkUrl?.trim() && (
                                                    <span className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                                        URL needed
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                        <div className="relative mt-1">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg className="h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M18.561 13.158c-1.102 0-2.135-.467-3.074-1.227l.228-1.076.008-.042c.207-1.143.849-3.06 2.839-3.06 1.492 0 2.703 1.212 2.703 2.703-.001 1.489-1.212 2.702-2.704 2.702zm0-8.14c-2.539 0-4.51 1.649-5.31 4.366-1.22-1.834-2.148-4.036-2.687-5.892H7.828v7.112c-.002 1.406-1.141 2.546-2.547 2.548-1.405-.002-2.543-1.143-2.545-2.548V3.492H0v7.112c0 2.914 2.37 5.303 5.281 5.303 2.913 0 5.283-2.389 5.283-5.303v-1.19c.529 1.107 1.182 2.229 1.974 3.221l-1.673 7.873h2.797l1.213-5.71c1.063.679 2.285 1.109 3.686 1.109 3 0 5.439-2.452 5.439-5.45 0-3.002-2.439-5.453-5.439-5.453z"/>
                                                </svg>
                                            </div>
                                            <Input
                                                id="upworkUrl"
                                                value={data.upworkUrl as string}
                                                onChange={(e) => setData('upworkUrl', e.target.value)}
                                                className="pl-10"
                                                placeholder="https://upwork.com/freelancers/yourusername"
                                                disabled={!data.upworkEnabled}
                                            />
                                        </div>
                                        {errors.upworkUrl && (
                                            <p className="mt-1 text-sm text-red-600">{errors.upworkUrl}</p>
                                        )}
                                    </div>

                                    {/* Freelancer */}
                                    <div>
                                        <div className="flex items-center justify-between mb-2">
                                            <Label htmlFor="freelancerUrl">Freelancer</Label>
                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    id="freelancerEnabled"
                                                    checked={data.freelancerEnabled}
                                                    disabled={isSwitchDisabled('freelancerEnabled')}
                                                    onCheckedChange={(checked) => handleSwitchChange('freelancerEnabled', checked)}
                                                />
                                                <Label htmlFor="freelancerEnabled" className="text-sm text-gray-600">
                                                    {data.freelancerEnabled ? 'Enabled' : 'Disabled'}
                                                </Label>
                                                {data.freelancerEnabled && !data.freelancerUrl?.trim() && (
                                                    <span className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                                        URL needed
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                        <div className="relative mt-1">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg className="h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M14.096 3.076c1.634.545 2.929 1.841 3.474 3.474.545 1.634.545 3.365 0 5-.545 1.634-1.841 2.929-3.474 3.474-1.634.545-3.365.545-5 0-1.634-.545-2.929-1.841-3.474-3.474-.545-1.634-.545-3.365 0-5 .545-1.634 1.841-2.929 3.474-3.474 1.634-.545 3.365-.545 5 0zm-2.548 7.924c-.552 0-1-.448-1-1s.448-1 1-1 1 .448 1 1-.448 1-1 1z"/>
                                                </svg>
                                            </div>
                                            <Input
                                                id="freelancerUrl"
                                                value={data.freelancerUrl as string}
                                                onChange={(e) => setData('freelancerUrl', e.target.value)}
                                                className="pl-10"
                                                placeholder="https://freelancer.com/u/yourusername"
                                                disabled={!data.freelancerEnabled}
                                            />
                                        </div>
                                        {errors.freelancerUrl && (
                                            <p className="mt-1 text-sm text-red-600">{errors.freelancerUrl}</p>
                                        )}
                                    </div>

                                    {/* PeoplePerHour */}
                                    <div>
                                        <div className="flex items-center justify-between mb-2">
                                            <Label htmlFor="peopleperhourUrl">PeoplePerHour</Label>
                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    id="peopleperhourEnabled"
                                                    checked={data.peopleperhourEnabled}
                                                    disabled={isSwitchDisabled('peopleperhourEnabled')}
                                                    onCheckedChange={(checked) => handleSwitchChange('peopleperhourEnabled', checked)}
                                                />
                                                <Label htmlFor="peopleperhourEnabled" className="text-sm text-gray-600">
                                                    {data.peopleperhourEnabled ? 'Enabled' : 'Disabled'}
                                                </Label>
                                                {data.peopleperhourEnabled && !data.peopleperhourUrl?.trim() && (
                                                    <span className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                                                        URL needed
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                        <div className="relative mt-1">
                                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg className="h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10zm-1-16h2v2h-2V6zm0 4h2v8h-2v-8z"/>
                                                </svg>
                                            </div>
                                            <Input
                                                id="peopleperhourUrl"
                                                value={data.peopleperhourUrl as string}
                                                onChange={(e) => setData('peopleperhourUrl', e.target.value)}
                                                className="pl-10"
                                                placeholder="https://peopleperhour.com/freelancer/yourusername"
                                                disabled={!data.peopleperhourEnabled}
                                            />
                                        </div>
                                        {errors.peopleperhourUrl && (
                                            <p className="mt-1 text-sm text-red-600">{errors.peopleperhourUrl}</p>
                                        )}
                                    </div>

                                    <div className="flex justify-end">
                                        <Button type="submit" className="bg-[#20B2AA] hover:bg-[#1a9994]" disabled={processing}>
                                            {processing ? 'Saving...' : 'Save Social Links'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Navbar Settings Tab */}
                    <TabsContent value="navbar">
                        <Tabs defaultValue="logo" className="w-full">
                            <TabsList className="mb-6">
                                <TabsTrigger value="logo">Logo Settings</TabsTrigger>
                                <TabsTrigger value="items">Navbar Items</TabsTrigger>
                                <TabsTrigger value="hireme">Hire Me Button</TabsTrigger>
                            </TabsList>

                            {/* Logo Settings Tab */}
                            <TabsContent value="logo">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Logo Settings</CardTitle>
                                        <CardDescription>
                                            Customize your portfolio logo appearance.
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <form onSubmit={handleLogoSubmit} className="space-y-6">
                                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                                <div className="col-span-1">
                                                    <div className="flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-200 rounded-lg">
                                                        <div className="mb-4">
                                                            <DynamicLogo
                                                                logoText={data.logo_text}
                                                                logoType={data.logo_type as 'text_only' | 'icon_only' | 'text_with_icon'}
                                                                logoIcon={data.logo_icon}
                                                                logoIconType={data.logo_icon_type as 'letter' | 'svg' | 'image'}
                                                                logoImage={selectedImagePreview || profile.logo_image}
                                                                logoColor={data.logo_color}
                                                            />
                                                        </div>
                                                        <p className="text-sm text-gray-500">
                                                            Logo Preview
                                                        </p>
                                                    </div>
                                                </div>

                                                <div className="col-span-2 space-y-4">
                                                    <div>
                                                        <Label htmlFor="logo_text">Logo Text</Label>
                                                        <Input
                                                            id="logo_text"
                                                            value={data.logo_text}
                                                            onChange={(e) => setData('logo_text', e.target.value)}
                                                            className="mt-1"
                                                        />
                                                        {errors.logo_text && (
                                                            <p className="mt-1 text-sm text-red-600">{errors.logo_text}</p>
                                                        )}
                                                    </div>

                                                    <div>
                                                        <Label htmlFor="logo_type">Logo Type</Label>
                                                        <Select
                                                            value={data.logo_type}
                                                            onValueChange={(value) => setData('logo_type', value)}
                                                        >
                                                            <SelectTrigger className="mt-1">
                                                                <SelectValue placeholder="Select logo type" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                <SelectItem value="text_only">Text Only</SelectItem>
                                                                <SelectItem value="icon_only">Icon Only</SelectItem>
                                                                <SelectItem value="text_with_icon">Text with Icon</SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                                        {errors.logo_type && (
                                                            <p className="mt-1 text-sm text-red-600">{errors.logo_type}</p>
                                                        )}
                                                    </div>

                                                    <div>
                                                        <Label htmlFor="logo_icon_type">Icon Type</Label>
                                                        <Select
                                                            value={data.logo_icon_type}
                                                            onValueChange={(value) => setData('logo_icon_type', value)}
                                                        >
                                                            <SelectTrigger className="mt-1">
                                                                <SelectValue placeholder="Select icon type" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                <SelectItem value="letter">Letter</SelectItem>
                                                                <SelectItem value="svg">SVG</SelectItem>
                                                                <SelectItem value="image">Custom Image (PNG, JPG, GIF)</SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                                        {errors.logo_icon_type && (
                                                            <p className="mt-1 text-sm text-red-600">{errors.logo_icon_type}</p>
                                                        )}
                                                    </div>

                                                    {data.logo_icon_type === 'letter' && (
                                                        <div>
                                                            <Label htmlFor="logo_icon">Icon Letter</Label>
                                                            <Input
                                                                id="logo_icon"
                                                                value={data.logo_icon}
                                                                onChange={(e) => setData('logo_icon', e.target.value)}
                                                                className="mt-1"
                                                                maxLength={1}
                                                            />
                                                            {errors.logo_icon && (
                                                                <p className="mt-1 text-sm text-red-600">{errors.logo_icon}</p>
                                                            )}
                                                        </div>
                                                    )}

                                                    {data.logo_icon_type === 'svg' && (
                                                        <div>
                                                            <Label htmlFor="logo_icon">SVG Code</Label>
                                                            <Input
                                                                id="logo_icon"
                                                                value={data.logo_icon}
                                                                onChange={(e) => setData('logo_icon', e.target.value)}
                                                                className="mt-1"
                                                            />
                                                            {errors.logo_icon && (
                                                                <p className="mt-1 text-sm text-red-600">{errors.logo_icon}</p>
                                                            )}
                                                        </div>
                                                    )}

                                                    {data.logo_icon_type === 'image' && (
                                                        <div>
                                                            <Label htmlFor="logo_image">Upload Logo Image</Label>
                                                            <Input
                                                                id="logo_image"
                                                                type="file"
                                                                accept="image/*"
                                                                onChange={(e) => {
                                                                    const file = e.target.files?.[0];
                                                                    if (file) {
                                                                        setData('logo_image', file);

                                                                        // Create preview URL for real-time preview
                                                                        const previewUrl = URL.createObjectURL(file);
                                                                        setSelectedImagePreview(previewUrl);
                                                                    } else {
                                                                        // Clear preview if no file selected
                                                                        setSelectedImagePreview(null);
                                                                    }
                                                                }}
                                                                className="mt-1"
                                                            />
                                                            <p className="mt-1 text-sm text-gray-500">
                                                                Supported formats: PNG, JPG, GIF, SVG, WebP (Max: 2MB)
                                                            </p>
                                                            {errors.logo_image && (
                                                                <p className="mt-1 text-sm text-red-600">{errors.logo_image}</p>
                                                            )}
                                                            {profile.logo_image && (
                                                                <div className="mt-2">
                                                                    <p className="text-sm text-gray-600">Current logo:</p>
                                                                    <img
                                                                        src={`/storage/${profile.logo_image}`}
                                                                        alt="Current logo"
                                                                        className="mt-1 h-12 w-auto object-contain border rounded"
                                                                    />
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}

                                                    <div>
                                                        <Label htmlFor="logo_color">Logo Text Color</Label>
                                                        <Select
                                                            value={showCustomColor ? 'custom' : data.logo_color}
                                                            onValueChange={(value) => {
                                                                if (value === 'custom') {
                                                                    setShowCustomColor(true);
                                                                } else {
                                                                    setShowCustomColor(false);
                                                                    setData('logo_color', value);
                                                                }
                                                            }}
                                                        >
                                                            <SelectTrigger className="mt-1">
                                                                <SelectValue placeholder="Select text color" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                <SelectItem value="#1f2937">
                                                                    <div className="flex items-center gap-2">
                                                                        <div className="w-4 h-4 rounded-full bg-gray-800"></div>
                                                                        <span>Dark Gray (Default)</span>
                                                                    </div>
                                                                </SelectItem>
                                                                <SelectItem value="#000000">
                                                                    <div className="flex items-center gap-2">
                                                                        <div className="w-4 h-4 rounded-full bg-black"></div>
                                                                        <span>Black</span>
                                                                    </div>
                                                                </SelectItem>
                                                                <SelectItem value="#374151">
                                                                    <div className="flex items-center gap-2">
                                                                        <div className="w-4 h-4 rounded-full bg-gray-700"></div>
                                                                        <span>Gray</span>
                                                                    </div>
                                                                </SelectItem>
                                                                <SelectItem value="#4B5563">
                                                                    <div className="flex items-center gap-2">
                                                                        <div className="w-4 h-4 rounded-full bg-gray-600"></div>
                                                                        <span>Light Gray</span>
                                                                    </div>
                                                                </SelectItem>
                                                                <SelectItem value="#ffffff">
                                                                    <div className="flex items-center gap-2">
                                                                        <div className="w-4 h-4 rounded-full bg-white border border-gray-300"></div>
                                                                        <span>White</span>
                                                                    </div>
                                                                </SelectItem>
                                                                <SelectItem value="#3b82f6">
                                                                    <div className="flex items-center gap-2">
                                                                        <div className="w-4 h-4 rounded-full bg-blue-500"></div>
                                                                        <span>Blue</span>
                                                                    </div>
                                                                </SelectItem>
                                                                <SelectItem value="#10b981">
                                                                    <div className="flex items-center gap-2">
                                                                        <div className="w-4 h-4 rounded-full bg-emerald-500"></div>
                                                                        <span>Green</span>
                                                                    </div>
                                                                </SelectItem>
                                                                <SelectItem value="#8b5cf6">
                                                                    <div className="flex items-center gap-2">
                                                                        <div className="w-4 h-4 rounded-full bg-violet-500"></div>
                                                                        <span>Purple</span>
                                                                    </div>
                                                                </SelectItem>
                                                                <SelectItem value="#ef4444">
                                                                    <div className="flex items-center gap-2">
                                                                        <div className="w-4 h-4 rounded-full bg-red-500"></div>
                                                                        <span>Red</span>
                                                                    </div>
                                                                </SelectItem>
                                                                <SelectItem value="#f59e0b">
                                                                    <div className="flex items-center gap-2">
                                                                        <div className="w-4 h-4 rounded-full bg-amber-500"></div>
                                                                        <span>Orange</span>
                                                                    </div>
                                                                </SelectItem>
                                                                <SelectItem value="#20B2AA">
                                                                    <div className="flex items-center gap-2">
                                                                        <div className="w-4 h-4 rounded-full" style={{ backgroundColor: '#20B2AA' }}></div>
                                                                        <span>Teal (Current)</span>
                                                                    </div>
                                                                </SelectItem>
                                                                <SelectItem value="custom">
                                                                    <div className="flex items-center gap-2">
                                                                        <div className="w-4 h-4 rounded-full border-2 border-dashed border-gray-400"></div>
                                                                        <span>Custom Color</span>
                                                                    </div>
                                                                </SelectItem>
                                                            </SelectContent>
                                                        </Select>

                                                        {showCustomColor && (
                                                            <div className="mt-3">
                                                                <Label htmlFor="custom_logo_color">Custom Color</Label>
                                                                <div className="flex gap-2 mt-1">
                                                                    <Input
                                                                        id="custom_logo_color"
                                                                        value={data.logo_color}
                                                                        onChange={(e) => setData('logo_color', e.target.value)}
                                                                        placeholder="#1f2937"
                                                                        className="flex-1"
                                                                    />
                                                                    <input
                                                                        type="color"
                                                                        value={data.logo_color}
                                                                        onChange={(e) => setData('logo_color', e.target.value)}
                                                                        className="w-10 h-10 p-1 border rounded cursor-pointer"
                                                                        title="Pick a color"
                                                                    />
                                                                </div>
                                                                <p className="mt-1 text-xs text-gray-500">
                                                                    Enter a hex color code (e.g., #1f2937) or use the color picker
                                                                </p>
                                                            </div>
                                                        )}

                                                        {errors.logo_color && (
                                                            <p className="mt-1 text-sm text-red-600">{errors.logo_color}</p>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="flex justify-end">
                                                <Button type="submit" className="bg-[#20B2AA] hover:bg-[#1a9994]" disabled={processing}>
                                                    {processing ? 'Saving...' : 'Save Logo Settings'}
                                                </Button>
                                            </div>
                                        </form>
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            {/* Navbar Items Tab */}
                            <TabsContent value="items">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Navbar Items</CardTitle>
                                        <CardDescription>
                                            Customize the navigation menu items.
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <form onSubmit={handleNavbarSubmit} className="space-y-6">
                                            <div className="space-y-4">
                                                <div className="flex justify-between items-center">
                                                    <h3 className="text-lg font-medium">Menu Items</h3>
                                                </div>

                                                <DndContext
                                                    sensors={sensors}
                                                    collisionDetection={closestCenter}
                                                    onDragEnd={handleDragEnd}
                                                >
                                                    <SortableContext
                                                        items={Array.isArray(navbarItems) ? navbarItems.map((_, index) => `item-${index}`) : []}
                                                        strategy={verticalListSortingStrategy}
                                                    >
                                                        <div className="space-y-2">
                                                            {Array.isArray(navbarItems) && navbarItems.map((item, index) => (
                                                                <SortableItem
                                                                    key={`item-${index}`}
                                                                    id={`item-${index}`}
                                                                    item={item}
                                                                    index={index}
                                                                    updateNavbarItem={updateNavbarItem}
                                                                />
                                                            ))}
                                                        </div>
                                                    </SortableContext>
                                                </DndContext>

                                                {(!Array.isArray(navbarItems) || navbarItems.length === 0) && (
                                                    <div className="text-center py-8 text-gray-500">
                                                        No menu items available.
                                                    </div>
                                                )}
                                            </div>

                                            <div className="flex justify-end">
                                                <Button type="submit" className="bg-[#20B2AA] hover:bg-[#1a9994]" disabled={processing}>
                                                    {processing ? 'Saving...' : 'Save Navbar Items'}
                                                </Button>
                                            </div>
                                        </form>
                                    </CardContent>
                                </Card>
                            </TabsContent>

                            {/* Hire Me Button Tab */}
                            <TabsContent value="hireme">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Hire Me Button Settings</CardTitle>
                                        <CardDescription>
                                            Customize the hire me button that appears in the navbar.
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <form onSubmit={handleHireMeSubmit} className="space-y-6">
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div className="space-y-4">
                                                    <div>
                                                        <Label htmlFor="hire_me_text">Button Text</Label>
                                                        <Input
                                                            id="hire_me_text"
                                                            value={data.hire_me_text}
                                                            onChange={(e) => setData('hire_me_text', e.target.value)}
                                                            className="mt-1"
                                                            placeholder="Hire Me"
                                                        />
                                                        {errors.hire_me_text && (
                                                            <p className="mt-1 text-sm text-red-600">{errors.hire_me_text}</p>
                                                        )}
                                                    </div>

                                                    <div>
                                                        <Label htmlFor="hire_me_url">Button URL</Label>
                                                        <Input
                                                            id="hire_me_url"
                                                            value={data.hire_me_url}
                                                            onChange={(e) => setData('hire_me_url', e.target.value)}
                                                            className="mt-1"
                                                            placeholder="#contact"
                                                        />
                                                        {errors.hire_me_url && (
                                                            <p className="mt-1 text-sm text-red-600">{errors.hire_me_url}</p>
                                                        )}
                                                    </div>

                                                    <div className="flex items-center space-x-2">
                                                        <Switch
                                                            id="hire_me_enabled"
                                                            checked={data.hire_me_enabled}
                                                            onCheckedChange={(checked) => setData('hire_me_enabled', checked)}
                                                        />
                                                        <Label htmlFor="hire_me_enabled">Enable Hire Me Button</Label>
                                                    </div>
                                                </div>

                                                <div className="flex flex-col items-center justify-center p-6 border-2 border-dashed border-gray-200 rounded-lg">
                                                    <div className="mb-4">
                                                        <Button
                                                            type="button"
                                                            className={`bg-[#20B2AA] hover:bg-[#1a9994] text-white px-6 py-2.5 text-sm ${!data.hire_me_enabled ? 'opacity-50' : ''}`}
                                                            disabled={!data.hire_me_enabled}
                                                        >
                                                            {data.hire_me_text}
                                                        </Button>
                                                    </div>
                                                    <p className="text-sm text-gray-500 text-center">Button Preview</p>
                                                </div>
                                            </div>

                                            <Button
                                                type="submit"
                                                disabled={processing}
                                                className="bg-teal-600 hover:bg-teal-700 text-white"
                                            >
                                                {processing ? 'Saving...' : 'Save Hire Me Settings'}
                                            </Button>
                                        </form>
                                    </CardContent>
                                </Card>
                            </TabsContent>
                        </Tabs>
                    </TabsContent>
                </Tabs>
            </div>
        </AdminLayout>
    );
}