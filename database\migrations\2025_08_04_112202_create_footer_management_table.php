<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('footer_management', function (Blueprint $table) {
            $table->id();

            // Logo Settings (same as navbar logo)
            $table->string('logo_text')->default('Portfolio');
            $table->string('logo_type')->default('text_with_icon'); // text_only, icon_only, text_with_icon
            $table->string('logo_icon')->default('P'); // Letter or path to SVG/PNG
            $table->string('logo_icon_type')->default('letter'); // letter, svg, image
            $table->string('logo_color')->default('#20B2AA');

            // Footer Content
            $table->text('description')->default('Creating exceptional digital experiences through innovative design and development solutions.');
            $table->string('copyright_text')->default('© {year} {company}. All rights reserved.');
            $table->string('company_name')->default('Your Portfolio');

            // Social Links
            $table->string('twitter_url')->nullable();
            $table->string('github_url')->nullable();
            $table->string('linkedin_url')->nullable();
            $table->string('facebook_url')->nullable();
            $table->string('instagram_url')->nullable();
            $table->string('dribbble_url')->nullable();
            $table->string('behance_url')->nullable();
            $table->string('upassign_url')->nullable();
            $table->string('fiverr_url')->nullable();
            $table->string('upwork_url')->nullable();
            $table->string('freelancer_url')->nullable();
            $table->string('peopleperhour_url')->nullable();

            // Social Links Visibility
            $table->boolean('twitter_enabled')->default(false);
            $table->boolean('github_enabled')->default(false);
            $table->boolean('linkedin_enabled')->default(false);
            $table->boolean('facebook_enabled')->default(false);
            $table->boolean('instagram_enabled')->default(false);
            $table->boolean('dribbble_enabled')->default(false);
            $table->boolean('behance_enabled')->default(false);
            $table->boolean('upassign_enabled')->default(false);
            $table->boolean('fiverr_enabled')->default(false);
            $table->boolean('upwork_enabled')->default(false);
            $table->boolean('freelancer_enabled')->default(false);
            $table->boolean('peopleperhour_enabled')->default(false);

            // Footer Links (stored as JSON)
            $table->json('footer_links')->nullable(); // Array of {title, url, enabled}

            // Settings
            $table->boolean('show_social_links')->default(true);
            $table->boolean('show_footer_links')->default(true);
            $table->boolean('show_scroll_to_top')->default(true);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('footer_management');
    }
};
