<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HeroSection extends Model
{
    use HasFactory;

    protected $table = 'hero_section';

    protected $fillable = [
        'page_title',
        'title',
        'about',
        'avatar',
        'resume',
        'github_url',
        'github_enabled',
        'linkedin_url',
        'linkedin_enabled',
        'twitter_url',
        'twitter_enabled',
        'facebook_url',
        'facebook_enabled',
        'instagram_url',
        'instagram_enabled',
        'dribbble_url',
        'dribbble_enabled',
        'behance_url',
        'behance_enabled',
        'fiverr_url',
        'fiverr_enabled',
        'upwork_url',
        'upwork_enabled',
        'freelancer_url',
        'freelancer_enabled',
        'peopleperhour_url',
        'peopleperhour_enabled',
        'upassign_url',
        'upassign_enabled',
        'years_experience',
        'projects_completed',
        'is_available',
        'cta_text',
        'cta_secondary_text',
        'cta_url',
        'cta_secondary_url',
        'logo_text',
        'logo_type',
        'logo_icon',
        'logo_icon_type',
        'logo_image',
        'logo_color',
        'navbar_items',
        'hire_me_text',
        'hire_me_url',
        'hire_me_enabled',
    ];

    protected $casts = [
        'is_available' => 'boolean',
        'github_enabled' => 'boolean',
        'linkedin_enabled' => 'boolean',
        'twitter_enabled' => 'boolean',
        'facebook_enabled' => 'boolean',
        'instagram_enabled' => 'boolean',
        'dribbble_enabled' => 'boolean',
        'behance_enabled' => 'boolean',
        'fiverr_enabled' => 'boolean',
        'upwork_enabled' => 'boolean',
        'freelancer_enabled' => 'boolean',
        'peopleperhour_enabled' => 'boolean',
        'upassign_enabled' => 'boolean',
        'years_experience' => 'integer',
        'projects_completed' => 'integer',
        'navbar_items' => 'array',
        'hire_me_enabled' => 'boolean',
    ];

    /**
     * Get the first (and should be only) hero section record.
     * Create one with defaults if it doesn't exist.
     *
     * @return HeroSection
     */
    public static function getOrCreate()
    {
        // Always get fresh data from database - get the most recently updated record
        $record = static::orderBy('updated_at', 'desc')->first();

        if (!$record) {
            $record = static::create([
                'page_title' => 'Ratul Ahmed - Portfolio',
                'title' => 'Senior Full Stack Developer',
                'about' => 'I am a dedicated full-stack developer who loves turning complex problems into simple, beautiful, and intuitive solutions. With expertise in both front-end and back-end development, I create scalable web applications that provide exceptional user experiences.',
                'years_experience' => 5,
                'projects_completed' => 50,
                'is_available' => true,
                'cta_text' => 'Hire Me Now',
                'cta_secondary_text' => 'Download CV',
                'cta_url' => '#contact',
                'cta_secondary_url' => '#portfolio',
                'logo_text' => 'Portfolio',
                'logo_type' => 'text_with_icon',
                'logo_icon' => 'P',
                'logo_icon_type' => 'letter',
                'logo_color' => '#1f2937',
                'github_url' => 'https://github.com',
                'github_enabled' => true,
                'linkedin_url' => 'https://linkedin.com',
                'linkedin_enabled' => true,
                'twitter_url' => 'https://twitter.com',
                'twitter_enabled' => true,
                'facebook_url' => 'https://facebook.com',
                'facebook_enabled' => true,
                'instagram_url' => 'https://instagram.com',
                'instagram_enabled' => true,
                'dribbble_url' => 'https://dribbble.com',
                'dribbble_enabled' => true,
                'behance_url' => 'https://behance.net',
                'behance_enabled' => true,
                'fiverr_url' => 'https://fiverr.com',
                'fiverr_enabled' => true,
                'upwork_url' => 'https://upwork.com',
                'upwork_enabled' => true,
                'freelancer_url' => 'https://freelancer.com',
                'freelancer_enabled' => true,
                'peopleperhour_url' => 'https://peopleperhour.com',
                'peopleperhour_enabled' => true,
                'upassign_url' => 'https://upassign.com',
                'upassign_enabled' => true,
                'navbar_items' => [
                    ['title' => 'Home', 'href' => 'home'],
                    ['title' => 'Services', 'href' => 'services'],
                    ['title' => 'Projects', 'href' => 'projects'],
                    ['title' => 'Skills', 'href' => 'skills'],
                    ['title' => 'Resume', 'href' => 'resume'],
                    ['title' => 'Testimonials', 'href' => 'testimonials'],
                    ['title' => 'Contact', 'href' => 'contact']
                ],
                'hire_me_text' => 'Hire Me',
                'hire_me_url' => '#contact',
                'hire_me_enabled' => true,
            ]);
        }

        return $record;
    }

    /**
     * Get default hero section data for fallback when database is empty
     *
     * @return array
     */
    public static function getDefaults()
    {
        return [
            'page_title' => 'Ratul Ahmed - Portfolio',
            'title' => 'Senior Full Stack Developer',
            'about' => 'I am a dedicated full-stack developer who loves turning complex problems into simple, beautiful, and intuitive solutions. With expertise in both front-end and back-end development, I create scalable web applications that provide exceptional user experiences.',
            'years_experience' => 5,
            'projects_completed' => 50,
            'is_available' => true,
            'cta_text' => 'Hire Me Now',
            'cta_secondary_text' => 'Download CV',
            'cta_url' => '#contact',
            'cta_secondary_url' => '#portfolio',
            'logo_text' => 'Portfolio',
            'logo_type' => 'text_with_icon',
            'logo_icon' => 'P',
            'logo_icon_type' => 'letter',
            'logo_color' => '#20B2AA',
            'github_url' => 'https://github.com',
            'github_enabled' => true,
            'linkedin_url' => 'https://linkedin.com',
            'linkedin_enabled' => true,
            'twitter_url' => 'https://twitter.com',
            'twitter_enabled' => true,
            'facebook_url' => 'https://facebook.com',
            'facebook_enabled' => true,
            'instagram_url' => 'https://instagram.com',
            'instagram_enabled' => true,
            'dribbble_url' => 'https://dribbble.com',
            'dribbble_enabled' => true,
            'behance_url' => 'https://behance.net',
            'behance_enabled' => true,
            'fiverr_url' => 'https://fiverr.com',
            'fiverr_enabled' => true,
            'upwork_url' => 'https://upwork.com',
            'upwork_enabled' => true,
            'freelancer_url' => 'https://freelancer.com',
            'freelancer_enabled' => true,
            'peopleperhour_url' => 'https://peopleperhour.com',
            'peopleperhour_enabled' => true,
            'upassign_url' => 'https://upassign.com',
            'upassign_enabled' => true,
            'navbar_items' => [
                ['title' => 'Home', 'href' => 'home'],
                ['title' => 'Services', 'href' => 'services'],
                ['title' => 'Projects', 'href' => 'projects'],
                ['title' => 'Skills', 'href' => 'skills'],
                ['title' => 'Resume', 'href' => 'resume'],
                ['title' => 'Testimonials', 'href' => 'testimonials'],
                ['title' => 'Contact', 'href' => 'contact']
            ],
            'hire_me_text' => 'Hire Me',
            'hire_me_url' => '#contact',
            'hire_me_enabled' => true,
        ];
    }
}
