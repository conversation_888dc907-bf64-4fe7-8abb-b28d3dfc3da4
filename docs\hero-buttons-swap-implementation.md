# Hero Section Buttons Swap Implementation

## Overview
Successfully swapped the hero section buttons functionality so that "View Portfolio" is now "Download CV" and vice versa.

## Changes Made

### 1. **Button Functionality Swap**
- **Primary Button** (left): Now "Download CV" - downloads the uploaded resume
- **Secondary Button** (right): Now "View Portfolio" - navigates to portfolio section

### 2. **Updated Default Values**

#### HeroSection Model (`app/Models/HeroSection.php`)
```php
// Before:
'cta_text' => 'Hire Me Now',
'cta_secondary_text' => 'Download CV',
'cta_url' => '#contact',
'cta_secondary_url' => '#portfolio',

// After:
'cta_text' => 'Download CV',
'cta_secondary_text' => 'View Portfolio', 
'cta_url' => '#download-cv',
'cta_secondary_url' => '#portfolio',
```

#### Admin Profile Form (`resources/js/pages/admin/profile.tsx`)
```javascript
// Before:
ctaText: profile.ctaText || 'View Work',
ctaSecondaryText: profile.ctaSecondaryText || 'Download CV',
ctaUrl: profile.ctaUrl || '#works',
ctaSecondaryUrl: profile.ctaSecondaryUrl || '#',

// After:
ctaText: profile.ctaText || 'Download CV',
ctaSecondaryText: profile.ctaSecondaryText || 'View Portfolio',
ctaUrl: profile.ctaUrl || '#download-cv', 
ctaSecondaryUrl: profile.ctaSecondaryUrl || '#portfolio',
```

#### Welcome Page Defaults (`resources/js/pages/welcome.tsx`)
```javascript
// Before:
const defaultCtaText = 'View Work';
const defaultCtaSecondaryText = 'Download CV';
const defaultCtaUrl = '#works';
const defaultCtaSecondaryUrl = '#';

// After:
const defaultCtaText = 'Download CV';
const defaultCtaSecondaryText = 'View Portfolio';
const defaultCtaUrl = '#download-cv';
const defaultCtaSecondaryUrl = '#portfolio';
```

#### Database Seeder (`database/seeders/HeroSectionSeeder.php`)
```php
// Before:
'cta_text' => 'Contact Me',
'cta_secondary_text' => 'View Portfolio',

// After:
'cta_text' => 'Download CV',
'cta_secondary_text' => 'View Portfolio',
```

### 3. **Hero Section Button Implementation**

#### Primary Button (Download CV)
```javascript
<ActionButton 
    onClick={() => {
        if (profile?.resume) {
            window.open('/download-hero-resume', '_blank');
        } else {
            alert('Resume file not available for download');
        }
    }}
    className="relative z-10 shadow-lg..."
    fullWidth
>
    <Download className="w-4 sm:w-5 h-4 sm:h-5 mr-1.5 sm:mr-2 text-white transition-colors flex-shrink-0" />
    <span className="block">{ctaText}</span>
</ActionButton>
```

#### Secondary Button (View Portfolio)
```javascript
<ActionButton 
    variant="outline" 
    icon={false}
    href={ctaSecondaryUrl}
    className="group relative z-10 bg-white dark:bg-gray-800..."
    fullWidth
>
    <span className="block">{ctaSecondaryText}</span>
</ActionButton>
```

## Current Button Layout

```
[Download CV] [View Portfolio]
     ↓              ↓
  Downloads      Navigates to
   Resume        Portfolio
```

## How It Works

### For Admin Users:
1. Upload resume in `/admin/profile` 
2. Configure button text and URLs in admin panel
3. Primary button text controls "Download CV" button
4. Secondary button text controls "View Portfolio" button

### For Website Visitors:
1. **Download CV Button** (Primary):
   - Downloads the resume uploaded in admin settings
   - Shows error if no resume is uploaded
   - Uses `/download-hero-resume` route

2. **View Portfolio Button** (Secondary):
   - Navigates to portfolio section or configured URL
   - Uses `ctaSecondaryUrl` setting (default: `#portfolio`)

## Admin Configuration

In the admin panel (`/admin/profile`), the CTA buttons section now controls:
- **Primary Button Text**: The "Download CV" button text
- **Primary Button URL**: Not used (download functionality is hardcoded)
- **Secondary Button Text**: The "View Portfolio" button text  
- **Secondary Button URL**: Where the "View Portfolio" button navigates

## Testing

1. **Test Download**: Click "Download CV" on homepage
2. **Test Navigation**: Click "View Portfolio" on homepage
3. **Test Admin**: Update button text in admin panel
4. **Test Error**: Try download when no resume is uploaded

## Backward Compatibility

- Existing configurations will continue to work
- Button text can still be customized in admin panel
- URLs can still be configured for the portfolio button
- Resume upload/download functionality remains unchanged

## Notes

- The primary button now has a download icon
- The secondary button is styled as an outline button
- Error handling is in place for missing resume files
- All existing functionality is preserved
