<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HeroSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class ProfileController extends Controller
{
    public function index()
    {
        $heroSection = HeroSection::getOrCreate();

        return Inertia::render('admin/Profile', [
            'profile' => [
                'id' => $heroSection->id,
                'pageTitle' => $heroSection->page_title,
                'title' => $heroSection->title,
                'about' => $heroSection->about,
                'yearsExperience' => $heroSection->years_experience,
                'projectsCompleted' => $heroSection->projects_completed,
                'isAvailable' => $heroSection->is_available,
                'ctaText' => $heroSection->cta_text,
                'ctaSecondaryText' => $heroSection->cta_secondary_text,
                'ctaUrl' => $heroSection->cta_url,
                'ctaSecondaryUrl' => $heroSection->cta_secondary_url,
                'avatar' => $heroSection->avatar ? Storage::url($heroSection->avatar) : null,
                'resume' => $heroSection->resume ? Storage::url($heroSection->resume) : null,
                'githubUrl' => $heroSection->github_url,
                'githubEnabled' => $heroSection->github_enabled,
                'linkedinUrl' => $heroSection->linkedin_url,
                'linkedinEnabled' => $heroSection->linkedin_enabled,
                'twitterUrl' => $heroSection->twitter_url,
                'twitterEnabled' => $heroSection->twitter_enabled,
                'facebookUrl' => $heroSection->facebook_url,
                'facebookEnabled' => $heroSection->facebook_enabled,
                'instagramUrl' => $heroSection->instagram_url,
                'instagramEnabled' => $heroSection->instagram_enabled,
                'dribbbleUrl' => $heroSection->dribbble_url,
                'dribbbleEnabled' => $heroSection->dribbble_enabled,
                'behanceUrl' => $heroSection->behance_url,
                'behanceEnabled' => $heroSection->behance_enabled,
                'fiverrUrl' => $heroSection->fiverr_url,
                'fiverrEnabled' => $heroSection->fiverr_enabled,
                'upworkUrl' => $heroSection->upwork_url,
                'upworkEnabled' => $heroSection->upwork_enabled,
                'freelancerUrl' => $heroSection->freelancer_url,
                'freelancerEnabled' => $heroSection->freelancer_enabled,
                'peopleperhourUrl' => $heroSection->peopleperhour_url,
                'peopleperhourEnabled' => $heroSection->peopleperhour_enabled,
                'upassignUrl' => $heroSection->upassign_url,
                'upassignEnabled' => $heroSection->upassign_enabled,
                'logo_text' => $heroSection->logo_text,
                'logo_type' => $heroSection->logo_type,
                'logo_icon' => $heroSection->logo_icon,
                'logo_icon_type' => $heroSection->logo_icon_type,
                'logo_image' => $heroSection->logo_image,
                'logo_color' => $heroSection->logo_color,
                'navbar_items' => is_array($heroSection->navbar_items) ? $heroSection->navbar_items :
                    (is_string($heroSection->navbar_items) ? json_decode($heroSection->navbar_items, true) : []),
                'hire_me_text' => $heroSection->hire_me_text,
                'hire_me_url' => $heroSection->hire_me_url,
                'hire_me_enabled' => $heroSection->hire_me_enabled,
            ],
        ]);
    }

    public function update(Request $request)
    {
        $request->validate([
            'pageTitle' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'about' => 'required|string',
            'yearsExperience' => 'required|integer|min:0',
            'projectsCompleted' => 'required|integer|min:0',
            'githubUrl' => 'nullable|url|max:255',
            'linkedinUrl' => 'nullable|url|max:255',
            'twitterUrl' => 'nullable|url|max:255',
            'avatar' => 'nullable|image|max:2048',
            'resume' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
        ]);

        $heroSection = HeroSection::getOrCreate();

        // Update text fields
        $heroSection->page_title = $request->pageTitle;
        $heroSection->title = $request->title;
        $heroSection->about = $request->about;
        $heroSection->years_experience = $request->yearsExperience;
        $heroSection->projects_completed = $request->projectsCompleted;
        $heroSection->github_url = $request->githubUrl;
        $heroSection->linkedin_url = $request->linkedinUrl;
        $heroSection->twitter_url = $request->twitterUrl;

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if it exists
            if ($heroSection->avatar) {
                Storage::delete('public/' . $heroSection->avatar);
            }

            $path = $request->file('avatar')->store('avatars', 'public');
            $heroSection->avatar = $path;
        }

        // Handle resume upload
        if ($request->hasFile('resume')) {
            $request->validate([
                'resume' => 'file|mimes:pdf,doc,docx|max:10240', // 10MB max
            ]);

            // Delete old resume if it exists
            if ($heroSection->resume) {
                Storage::delete('public/' . $heroSection->resume);
            }

            $path = $request->file('resume')->store('resumes', 'public');
            $heroSection->resume = $path;
        }

        $heroSection->save();

        return redirect()->back()->with('success', 'Profile updated successfully.');
    }

    /**
     * Download the hero section resume file
     */
    public function downloadHeroResume()
    {
        $heroSection = HeroSection::getOrCreate();

        if (!$heroSection->resume || !Storage::disk('public')->exists($heroSection->resume)) {
            return response()->json(['error' => 'Resume file not found'], 404);
        }

        $originalName = basename($heroSection->resume);
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $downloadName = 'resume.' . $extension;

        return Storage::disk('public')->download($heroSection->resume, $downloadName);
    }

    /**
     * Delete the hero section resume file
     */
    public function deleteHeroResume()
    {
        $heroSection = HeroSection::getOrCreate();

        if ($heroSection->resume && Storage::disk('public')->exists($heroSection->resume)) {
            Storage::disk('public')->delete($heroSection->resume);
        }

        $heroSection->resume = null;
        $heroSection->save();

        return response()->json(['message' => 'Resume deleted successfully']);
    }

    public function updateHeroSection(Request $request)
    {
        $request->validate([
            'pageTitle' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'about' => 'required|string',
            'yearsExperience' => 'required|integer|min:0',
            'projectsCompleted' => 'required|integer|min:0',
            'isAvailable' => 'boolean',
            'ctaText' => 'required|string|max:255',
            'ctaSecondaryText' => 'required|string|max:255',
            'ctaUrl' => 'nullable|string|max:255',
            'ctaSecondaryUrl' => 'nullable|string|max:255',
            'avatar' => 'nullable|image|max:2048',
            'resume' => 'nullable|file|mimes:pdf,doc,docx|max:10240',
        ]);

        $heroSection = HeroSection::getOrCreate();

        // Update hero section fields
        $heroSection->page_title = $request->pageTitle;
        $heroSection->title = $request->title;
        $heroSection->about = $request->about;
        $heroSection->years_experience = $request->yearsExperience;
        $heroSection->projects_completed = $request->projectsCompleted;
        $heroSection->is_available = $request->isAvailable;
        $heroSection->cta_text = $request->ctaText;
        $heroSection->cta_secondary_text = $request->ctaSecondaryText;
        $heroSection->cta_url = $request->ctaUrl;
        $heroSection->cta_secondary_url = $request->ctaSecondaryUrl;

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if it exists
            if ($heroSection->avatar) {
                Storage::delete('public/' . $heroSection->avatar);
            }

            $path = $request->file('avatar')->store('avatars', 'public');
            $heroSection->avatar = $path;
        }

        // Handle resume upload
        if ($request->hasFile('resume')) {
            // Delete old resume if it exists
            if ($heroSection->resume) {
                Storage::delete('public/' . $heroSection->resume);
            }

            $path = $request->file('resume')->store('resumes', 'public');
            $heroSection->resume = $path;
        }

        $heroSection->save();

        return redirect()->back()->with('success', 'Profile updated successfully.');
    }

    public function updateSocialLinks(Request $request)
    {
        $request->validate([
            'githubUrl' => 'nullable|url|max:255',
            'githubEnabled' => 'boolean',
            'linkedinUrl' => 'nullable|url|max:255',
            'linkedinEnabled' => 'boolean',
            'twitterUrl' => 'nullable|url|max:255',
            'twitterEnabled' => 'boolean',
            'facebookUrl' => 'nullable|url|max:255',
            'facebookEnabled' => 'boolean',
            'instagramUrl' => 'nullable|url|max:255',
            'instagramEnabled' => 'boolean',
            'dribbbleUrl' => 'nullable|url|max:255',
            'dribbbleEnabled' => 'boolean',
            'behanceUrl' => 'nullable|url|max:255',
            'behanceEnabled' => 'boolean',
            'upassignUrl' => 'nullable|url|max:255',
            'upassignEnabled' => 'boolean',
            'fiverrUrl' => 'nullable|url|max:255',
            'fiverrEnabled' => 'boolean',
            'upworkUrl' => 'nullable|url|max:255',
            'upworkEnabled' => 'boolean',
            'freelancerUrl' => 'nullable|url|max:255',
            'freelancerEnabled' => 'boolean',
            'peopleperhourUrl' => 'nullable|url|max:255',
            'peopleperhourEnabled' => 'boolean',
        ]);

        $heroSection = HeroSection::getOrCreate();

        // Update social links URLs first
        $heroSection->github_url = $request->githubUrl;
        $heroSection->linkedin_url = $request->linkedinUrl;
        $heroSection->twitter_url = $request->twitterUrl;
        $heroSection->facebook_url = $request->facebookUrl;
        $heroSection->instagram_url = $request->instagramUrl;
        $heroSection->dribbble_url = $request->dribbbleUrl;
        $heroSection->behance_url = $request->behanceUrl;
        $heroSection->fiverr_url = $request->fiverrUrl;
        $heroSection->upwork_url = $request->upworkUrl;
        $heroSection->freelancer_url = $request->freelancerUrl;
        $heroSection->peopleperhour_url = $request->peopleperhourUrl;
        $heroSection->upassign_url = $request->upassignUrl;

        // Update enabled status from request
        $heroSection->github_enabled = $request->boolean('githubEnabled', false);
        $heroSection->linkedin_enabled = $request->boolean('linkedinEnabled', false);
        $heroSection->twitter_enabled = $request->boolean('twitterEnabled', false);
        $heroSection->facebook_enabled = $request->boolean('facebookEnabled', false);
        $heroSection->instagram_enabled = $request->boolean('instagramEnabled', false);
        $heroSection->dribbble_enabled = $request->boolean('dribbbleEnabled', false);
        $heroSection->behance_enabled = $request->boolean('behanceEnabled', false);
        $heroSection->fiverr_enabled = $request->boolean('fiverrEnabled', false);
        $heroSection->upwork_enabled = $request->boolean('upworkEnabled', false);
        $heroSection->freelancer_enabled = $request->boolean('freelancerEnabled', false);
        $heroSection->peopleperhour_enabled = $request->boolean('peopleperhourEnabled', false);
        $heroSection->upassign_enabled = $request->boolean('upassignEnabled', false);

        $heroSection->save();

        return redirect()->back()->with('success', 'Social links updated successfully.');
    }




} 