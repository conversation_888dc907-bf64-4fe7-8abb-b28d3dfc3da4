import { Head, useForm, router, usePage } from '@inertiajs/react';
import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion';
import { Download, Github, Linkedin, Twitter, Facebook, Instagram, Code, Smartphone, Search, BarChart3, FileText, Briefcase, GraduationCap, Mail, Phone, MapPin, ArrowRight, Menu, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { NavigationMenu, NavigationMenuItem, NavigationMenuLink, NavigationMenuList } from '@/components/ui/navigation-menu';
import { ProgressBar } from '@/components/ui/progress-bar';
import { SkillCard } from '@/components/ui/skill-card';
import { TimelineItem } from '@/components/ui/timeline-item';
import { TestimonialCard } from '@/components/ui/testimonial-card';
import { TestimonialGrid } from '@/components/ui/testimonial-grid';
import { ContactCard } from '@/components/ui/contact-card';
import { Link as ScrollLink, animateScroll as scroll } from 'react-scroll';
import { ThemeToggle } from '@/components/theme-toggle';
import { ThemeProvider } from '@/components/theme-provider';
import { useEffect, useState } from 'react';
import AOS from 'aos';
import 'aos/dist/aos.css';
import { Link } from '@inertiajs/react';
import { ProjectCard } from '@/components/ui/project-card';
import { ActionButton } from '@/components/ui/action-button';
import { DynamicFooter } from '@/components/dynamic-footer';
import { MobileMenu } from '@/components/ui/mobile-menu';
import { IconComponent } from '@/components/IconComponent';
import { DynamicLogo } from '@/components/ui/dynamic-logo';
import { toast } from 'sonner';

interface Service {
    id: number;
    title: string;
    description: string;
    icon: string;
    price: number;
    starting_price?: number;
    features: string[];
    is_active: boolean;
}

interface Project {
    id: number;
    title: string;
    description: string;
    image: string | null;
    category: string;
    technologies: string[];
}

interface ContentSection {
    badge: string;
    title: string;
    description: string;
    button_text: string;
}

interface ProfileData {
    page_title: string;
    title: string;
    about: string;
    years_experience: number;
    projects_completed: number;
    is_available: boolean;
    cta_text: string;
    cta_secondary_text: string;
    cta_url: string;
    cta_secondary_url: string;
    avatar: string;
    logo: {
        text: string;
        type: string;
        icon: string;
        icon_type: string;
        image: string | null;
        color: string;
    };
    navbar_items: Array<{
        title: string;
        href: string;
    }>;
    hire_me: {
        text: string;
        url: string;
        enabled: boolean;
    };
    social: {
        github: string | null;
        twitter: string | null;
        linkedin: string | null;
        facebook: string | null;
        instagram: string | null;
        dribbble: string | null;
        behance: string | null;
        upassign: string | null;
        fiverr: string | null;
        upwork: string | null;
        freelancer: string | null;
        peopleperhour: string | null;
    };
    contact: {
        email: string;
        phone: string;
        location: string;
    };
}

interface Props {
    services: Service[];
    projects: Project[];
    testimonials: any[];
    skills: {
        progress: Array<{ label: string; percentage: number }>;
        card: Array<{ skill: string; percentage: number }>;
    };
    experiences: any[];
    education: any[];
    profile: ProfileData | null;
    servicesContent: ContentSection;
    projectsContent: ContentSection;
    skillsContent: {
        badge: string;
        title: string;
        description: string;
    };
    testimonialsContent: {
        badge: string;
        title: string;
        description: string;
    };
    resumeContent: {
        section_badge: string;
        section_title: string;
        section_description: string;
        experience_title: string;
        education_title: string;
        download_button_text: string;
        resume_file_path: string | null;
    };
    footerData: any;
}

export default function Welcome({
    services,
    projects,
    testimonials,
    skills,
    experiences,
    education,
    profile,
    servicesContent,
    projectsContent,
    skillsContent,
    testimonialsContent,
    resumeContent,
    footerData
}: Props) {
    const [activeFilter, setActiveFilter] = useState('All');
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [activeSection, setActiveSection] = useState('home');
    const [showScrollTop, setShowScrollTop] = useState(false);

    // Access flash messages from Inertia
    const { flash } = usePage().props as any;

    // Contact form handling
    const { data: contactData, setData: setContactData, post: postContact, processing: contactProcessing, errors: contactErrors, reset: resetContact } = useForm({
        name: '',
        email: '',
        subject: '',
        message: '',
    });

    // Handle contact form submission
    const handleContactSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        postContact(route('contact.store'), {
            onSuccess: (page) => {
                // Check for flash success message
                const flashSuccess = (page.props as any).flash?.success;
                if (flashSuccess) {
                    toast.success('Message sent successfully!', {
                        description: flashSuccess,
                        duration: 5000,
                    });
                } else {
                    toast.success('Message sent successfully!', {
                        description: 'Thank you for your message. We will get back to you soon.',
                        duration: 5000,
                    });
                }
                resetContact();
            },
            onError: (errors) => {
                console.error('Contact form errors:', errors);
                toast.error('Failed to send message', {
                    description: 'Please check the form for errors and try again.',
                    duration: 5000,
                });
            },
        });
    };

    // Scroll animation hooks
    const { scrollY } = useScroll();
    const navbarBackground = useTransform(
        scrollY, 
        [0, 100], 
        ["rgba(255, 255, 255, 0.8)", "rgba(255, 255, 255, 0.95)"]
    );
    const navbarBackgroundDark = useTransform(
        scrollY, 
        [0, 100], 
        ["rgba(17, 24, 39, 0.85)", "rgba(17, 24, 39, 0.98)"]
    );
    const navbarHeight = useTransform(scrollY, [0, 100], ["80px", "64px"]);
    const navbarShadow = useTransform(
        scrollY, 
        [0, 100], 
        ["0 0 0 rgba(0, 0, 0, 0)", "0 4px 20px rgba(0, 0, 0, 0.1)"]
    );
    const navbarShadowDark = useTransform(
        scrollY, 
        [0, 100], 
        ["0 0 0 rgba(0, 0, 0, 0)", "0 4px 20px rgba(0, 0, 0, 0.2)"]
    );

    // Default values in case profile data is not available
    const defaultTitle = 'Creative Designer & Developer';
    const defaultAbout = 'I create exceptional digital experiences that solve complex problems and connect people through elegant, user-focused design.';
    const defaultCtaText = 'View Work';
    const defaultCtaSecondaryText = 'Download CV';
    const defaultCtaUrl = '#works';
    const defaultCtaSecondaryUrl = '#';
    const defaultIsAvailable = true;
    const defaultNavItems = [
        { title: 'Home', href: 'home' },
        { title: 'Services', href: 'services' },
        { title: 'Works', href: 'works' },
        { title: 'Skills', href: 'skills' },
        { title: 'Resume', href: 'resume' },
        { title: 'Testimonials', href: 'testimonials' },
        { title: 'Contact', href: 'contact' }
    ];
    
    const yearsExperience = profile?.years_experience ?? 5;
    const projectsCompleted = profile?.projects_completed ?? 50;
    const isAvailable = profile?.is_available !== undefined ? profile.is_available : defaultIsAvailable;

    // Debug logging (remove in production)
    if (typeof window !== 'undefined') {
        console.log('🔍 Availability Debug:', {
            'profile.is_available': profile?.is_available,
            'typeof profile.is_available': typeof profile?.is_available,
            'defaultIsAvailable': defaultIsAvailable,
            'final isAvailable': isAvailable,
            'profile object': profile
        });
    }
    const title = profile?.title ?? defaultTitle;
    const about = profile?.about ?? defaultAbout;
    const ctaText = profile?.cta_text ?? defaultCtaText;
    const ctaSecondaryText = profile?.cta_secondary_text ?? defaultCtaSecondaryText;
    const ctaUrl = profile?.cta_url ?? defaultCtaUrl;
    const ctaSecondaryUrl = profile?.cta_secondary_url ?? defaultCtaSecondaryUrl;
    const avatarUrl = profile?.avatar ?? '/images/Profile.png';
    const githubUrl = profile?.social?.github;
    const twitterUrl = profile?.social?.twitter;
    const linkedinUrl = profile?.social?.linkedin;
    const facebookUrl = profile?.social?.facebook;
    const instagramUrl = profile?.social?.instagram;
    const dribbbleUrl = profile?.social?.dribbble;
    const behanceUrl = profile?.social?.behance;
    const upassignUrl = profile?.social?.upassign;
    const fiverrUrl = profile?.social?.fiverr;
    const upworkUrl = profile?.social?.upwork;
    const freelancerUrl = profile?.social?.freelancer;
    const peopleperhourUrl = profile?.social?.peopleperhour;

    // Ensure navItems is always an array
    let navItems = defaultNavItems;
    if (profile?.navbar_items) {
        if (Array.isArray(profile.navbar_items)) {
            navItems = profile.navbar_items;
        } else if (typeof profile.navbar_items === 'string') {
            try {
                const parsed = JSON.parse(profile.navbar_items);
                if (Array.isArray(parsed)) {
                    navItems = parsed;
                }
            } catch (e) {
                console.warn('Failed to parse navbar_items JSON:', e);
            }
        }
    }
    
    // Logo settings
    const logoText = profile?.logo?.text ?? 'Portfolio';
    const logoType = profile?.logo?.type ?? 'text_with_icon';
    const logoIcon = profile?.logo?.icon ?? 'P';
    const logoIconType = profile?.logo?.icon_type ?? 'letter';
    const logoImage = profile?.logo?.image ?? null;
    const logoColor = profile?.logo?.color ?? '#20B2AA';

    // Hire me button settings
    const hireMeText = profile?.hire_me?.text ?? 'Hire Me';
    const hireMeUrl = profile?.hire_me?.url ?? '#contact';
    const hireMeEnabled = profile?.hire_me?.enabled ?? true;

    // Get unique categories from actual projects (fully dynamic like projects page)
    const projectCategories = projects.length > 0
        ? [...new Set(projects.map(project => project.category).filter(Boolean))].sort()
        : [];

    // Filter projects based on active filter - ALWAYS limit to maximum 6 cards
    const filteredProjects = activeFilter === 'All'
        ? projects.slice(0, 6) // Show first 6 when "All" is selected
        : projects.filter(project => project.category === activeFilter).slice(0, 6); // Show first 6 matching projects when category is selected

    useEffect(() => {
        AOS.init({
            duration: 1000,
            once: true,
            easing: 'ease-out',
        });
    }, []);
    
    // Handle URL query parameters for section scrolling
    useEffect(() => {
        const queryParams = new URLSearchParams(window.location.search);
        const section = queryParams.get('section');
        
        if (section) {
            const element = document.getElementById(section);
            if (element) {
                scroll.scrollTo(element.offsetTop - 100, {
                    duration: 800,
                    smooth: true,
                });
                
                // Clean the URL after scrolling (remove the query parameter)
                setTimeout(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);
                }, 1000);
            }
        }
    }, []);

    const scrollToTop = () => {
        scroll.scrollToTop({
            duration: 500,
            smooth: true
        });
    };

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.2,
                delayChildren: 0.3,
            }
        }
    };

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: {
                type: "spring",
                stiffness: 100
            }
        }
    };

    // Handle active section tracking
    useEffect(() => {
        const handleScroll = () => {
            // Ensure navItems is an array before mapping
            if (!Array.isArray(navItems)) return;

            const sections = navItems.map(item => item.href);
            const currentSection = sections.find(section => {
                // Handle both 'works' and 'projects' sections
                const elementId = section === 'projects' ? 'works' : section;
                const element = document.getElementById(elementId);
                if (!element) return false;

                const rect = element.getBoundingClientRect();
                return rect.top <= 150 && rect.bottom >= 150;
            });

            if (currentSection) {
                setActiveSection(currentSection);
            }
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, [navItems]);

    // Show/hide scroll to top button
    useEffect(() => {
        const handleScrollVisibility = () => {
            if (window.scrollY > 500) {
                setShowScrollTop(true);
            } else {
                setShowScrollTop(false);
            }
        };
        
        window.addEventListener('scroll', handleScrollVisibility);
        return () => window.removeEventListener('scroll', handleScrollVisibility);
    }, []);

    return (
        <ThemeProvider defaultTheme="light" storageKey="portfolio-theme">
            <Head title={profile?.page_title || "Portfolio - Creative Designer & Developer"}>
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600,700" rel="stylesheet" />
            </Head>

            <div className="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
                {/* Navigation */}
                <motion.nav 
                    style={{
                        backgroundColor: navbarBackground,
                        height: navbarHeight,
                        boxShadow: navbarShadow,
                    }}
                    className="fixed top-0 left-0 right-0 z-50 flex items-center justify-between px-4 sm:px-6 md:px-8 py-4 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 transition-all duration-300 dark:bg-transparent"
                >
                    <motion.div 
                        className="absolute inset-0 z-[-1] dark:bg-gray-900/95 backdrop-blur-sm"
                        style={{
                            backgroundColor: "transparent",
                            boxShadow: navbarShadowDark
                        }}
                        animate={{
                            backgroundColor: "transparent"
                        }}
                    />
                    <div className="absolute inset-0 z-[-1] bg-transparent dark:bg-gray-900/95 backdrop-blur-sm"></div>

                    <motion.div 
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5 }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={scrollToTop}
                        className="cursor-pointer"
                    >
                        <DynamicLogo
                            logoText={logoText}
                            logoType={logoType as 'text_only' | 'icon_only' | 'text_with_icon'}
                            logoIcon={logoIcon}
                            logoIconType={logoIconType as 'letter' | 'svg' | 'image'}
                            logoImage={logoImage}
                            logoColor={logoColor}
                        />
                    </motion.div>

                    {/* Desktop Navigation - Hidden on mobile */}
                    <div className="hidden md:block">
                        <NavigationMenu>
                            <NavigationMenuList className="flex gap-4 lg:gap-8">
                            {Array.isArray(navItems) && navItems.map((item, index) => (
                                <NavigationMenuItem key={item.title}>
                                    <motion.div
                                        initial={{ opacity: 0, y: -20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: index * 0.1 }}
                                        className="relative"
                                    >
                                        <motion.div whileHover={{ y: -2 }} transition={{ type: "spring", stiffness: 400 }}>
                                            <ScrollLink
                                                to={item.href === 'works' || item.href === 'projects' ? 'works' : item.href}
                                                spy={true}
                                                smooth={true}
                                                offset={-100}
                                                duration={500}
                                                className="text-gray-600 dark:text-gray-100 hover:text-[#20B2AA] dark:hover:text-[#20B2AA] cursor-pointer font-medium text-sm transition-colors px-2 py-1.5 block"
                                                activeClass="text-[#20B2AA] font-semibold"
                                            >
                                                {item.title}
                                            </ScrollLink>
                                        </motion.div>
                                        {(activeSection === item.href || (item.href === 'projects' && activeSection === 'works') || (item.href === 'works' && activeSection === 'projects')) && (
                                            <motion.div
                                                layoutId="activeIndicator"
                                                className="absolute bottom-[-3px] left-0 right-0 h-[2px] bg-[#20B2AA] dark:bg-[#20B2AA] dark:shadow-[0_0_4px_#20B2AA]"
                                                initial={{ opacity: 0 }}
                                                animate={{ opacity: 1 }}
                                                transition={{ duration: 0.3 }}
                                            />
                                        )}
                                    </motion.div>
                                </NavigationMenuItem>
                            ))}
                            </NavigationMenuList>
                        </NavigationMenu>
                    </div>

                    <motion.div 
                        className="flex items-center space-x-2"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        <ThemeToggle />
                        <Button 
                            variant="ghost" 
                            size="icon" 
                            className="md:hidden"
                            onClick={() => setIsMenuOpen(true)}
                        >
                            <Menu className="h-6 w-6" />
                        </Button>
                        {hireMeEnabled && (
                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                            >
                                <Button className="hidden md:inline-flex bg-[#20B2AA] hover:bg-[#1a9994] text-white shadow-md shadow-[#20B2AA]/20 dark:shadow-[#20B2AA]/40 hover:shadow-lg hover:shadow-[#20B2AA]/30 dark:hover:shadow-[#20B2AA]/50 transition-all duration-300">
                                    {hireMeUrl.startsWith('#') ? (
                                        <ScrollLink
                                            to={hireMeUrl.substring(1)}
                                            spy={true}
                                            smooth={true}
                                            offset={-100}
                                            duration={500}
                                            className="cursor-pointer"
                                        >
                                            {hireMeText}
                                        </ScrollLink>
                                    ) : (
                                        <a href={hireMeUrl} className="cursor-pointer">
                                            {hireMeText}
                                        </a>
                                    )}
                                </Button>
                            </motion.div>
                        )}
                    </motion.div>
                </motion.nav>

                {/* Mobile Menu */}
                <MobileMenu
                    isOpen={isMenuOpen}
                    onClose={() => setIsMenuOpen(false)}
                    navItems={Array.isArray(navItems) ? navItems : defaultNavItems}
                    logoText={logoText}
                    logoType={logoType as 'text_only' | 'icon_only' | 'text_with_icon'}
                    logoIcon={logoIcon}
                    logoIconType={logoIconType as 'letter' | 'svg'}
                    logoColor={logoColor}
                    hireMeText={hireMeText}
                    hireMeUrl={hireMeUrl}
                    hireMeEnabled={hireMeEnabled}
                />

                {/* Hero Section */}
                <motion.section 
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    className="min-h-[100vh] flex items-center justify-center mt-16 xs:mt-20 sm:mt-20 md:mt-10 px-3 xs:px-4 pb-10 sm:pb-10 md:pb-10 max-w-7xl mx-auto relative overflow-hidden"
                    id="home"
                >
                    {/* Background gradient effects */}
                    <div className="absolute top-0 left-0 w-full h-full">
                        <div className="absolute top-20 left-10 sm:left-20 w-36 xs:w-48 sm:w-72 h-36 xs:h-48 sm:h-72 bg-[#20B2AA]/10 rounded-full blur-3xl"></div>
                        <div className="absolute bottom-20 right-10 sm:right-20 w-40 xs:w-56 sm:w-96 h-40 xs:h-56 sm:h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 xs:gap-10 lg:gap-16 items-center relative w-full">
                        {/* Image Content - Order 1 on mobile (appears above text) */}
                        <div className="relative order-1 lg:order-2 w-full mt-0">
                            <motion.div
                                initial={{ scale: 0.5, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ duration: 0.5, delay: 0.2 }}
                                className="w-full h-[250px] xs:h-[300px] sm:h-[400px] md:h-[500px] lg:h-[550px] bg-[#7a261a] rounded-2xl xs:rounded-3xl overflow-hidden relative backdrop-blur-sm border border-orange-800/30 mx-auto max-w-sm sm:max-w-md md:max-w-lg lg:max-w-full"
                            >
                                {/* Profile image */}
                                <img 
                                    src={avatarUrl} 
                                    alt="Profile"
                                    className="w-full h-full object-contain object-center z-10 relative"
                                />
                                <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
                            </motion.div>
                            
                            {/* Stats cards - Positioned for responsive layout */}
                            <motion.div
                                initial={{ x: 20, opacity: 0 }}
                                animate={{ x: 0, opacity: 1 }}
                                transition={{ duration: 0.5, delay: 0.4 }}
                                className="absolute top-5 xs:top-6 right-5 md:top-8 md:right-8 bg-white dark:bg-gray-800 rounded-xl xs:rounded-2xl shadow-lg dark:shadow-gray-900/50 px-3 xs:px-4 sm:px-5 py-2 xs:py-3 sm:py-4 flex items-center gap-2 xs:gap-3 sm:gap-4 z-20 max-w-[170px] sm:max-w-[250px] border border-gray-100 dark:border-gray-700"
                            >
                                <div className="w-6 xs:w-8 sm:w-10 h-6 xs:h-8 sm:h-10 rounded-full bg-[#E6F7F6] dark:bg-[#20B2AA]/20 flex items-center justify-center flex-shrink-0">
                                    <motion.span 
                                        animate={{ scale: [1, 1.2, 1] }}
                                        transition={{ duration: 2, repeat: Infinity }}
                                        className="text-[#20B2AA] text-base xs:text-lg sm:text-xl"
                                    >
                                        ✓
                                    </motion.span>
                                </div>
                                <div className="min-w-0">
                                    <div className="font-semibold text-sm xs:text-base sm:text-lg text-gray-900 dark:text-white truncate">{projectsCompleted}+ Projects</div>
                                    <div className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 truncate">Completed</div>
                                </div>
                            </motion.div>
                            
                            <motion.div
                                initial={{ x: -20, opacity: 0 }}
                                animate={{ x: 0, opacity: 1 }}
                                transition={{ duration: 0.5, delay: 0.6 }}
                                className="absolute bottom-5 xs:bottom-6 left-5 md:bottom-8 md:left-8 bg-white dark:bg-gray-800 rounded-xl xs:rounded-2xl shadow-lg dark:shadow-gray-900/50 px-3 xs:px-4 sm:px-5 py-2 xs:py-3 sm:py-4 flex items-center gap-2 xs:gap-3 sm:gap-4 z-20 border border-gray-100 dark:border-gray-700"
                            >
                                <div className="w-6 xs:w-8 sm:w-10 h-6 xs:h-8 sm:h-10 rounded-full bg-[#E6F7F6] dark:bg-[#20B2AA]/20 flex items-center justify-center">
                                    <motion.span 
                                        animate={{ rotate: [0, 360] }}
                                        transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
                                        className="text-[#20B2AA] text-base xs:text-lg sm:text-xl"
                                    >
                                        ★
                                    </motion.span>
                                </div>
                                <div>
                                    <div className="font-semibold text-sm xs:text-base sm:text-lg text-gray-900 dark:text-white">{yearsExperience} Years</div>
                                    <div className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">Experience</div>
                                </div>
                            </motion.div>
                        </div>
                        
                        {/* Text Content - Order 2 on mobile (appears below image) */}
                        <motion.div variants={containerVariants} className="lg:order-1 order-2 w-full mt-4 xs:mt-6 sm:mt-0 flex flex-col items-center lg:items-start">
                            {isAvailable && (
                                <motion.div
                                    variants={itemVariants}
                                    className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-[#E6F7F6] text-[#20B2AA] text-xs xs:text-sm mb-4 xs:mb-5 sm:mb-6"
                                >
                                    <span className="w-2 h-2 rounded-full bg-[#20B2AA] animate-pulse"></span>
                                    Available for work
                                </motion.div>
                            )}

                            <motion.h1 
                                variants={itemVariants}
                                className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold leading-[1.1] tracking-tight mb-2 xs:mb-3 sm:mb-6 text-center lg:text-left"
                                dangerouslySetInnerHTML={{ __html: formatTitle(title) }}
                            />

                            <motion.p 
                                variants={itemVariants}
                                className="text-gray-600 dark:text-gray-300 text-sm xs:text-base sm:text-lg mb-3 xs:mb-4 sm:mb-8 max-w-xl text-center lg:text-left mx-auto lg:mx-0"
                            >
                                {about}
                            </motion.p>

                            <motion.div 
                                variants={itemVariants}
                                className="flex flex-wrap items-center justify-center lg:justify-start gap-4 mb-6 xs:mb-8 sm:mb-12 w-full max-w-xs xs:max-w-sm sm:max-w-md mx-auto lg:mx-0"
                            >
                                <div className="w-full xs:w-[48%] sm:w-auto">
                                    <ActionButton 
                                        href={ctaUrl} 
                                        className="relative z-10 shadow-lg shadow-[#20B2AA]/15 hover:shadow-xl hover:shadow-[#20B2AA]/25 transition-all duration-300 font-medium w-full text-center flex justify-center items-center text-sm sm:text-base py-3 px-6 sm:px-8"
                                        fullWidth
                                    >
                                        <span className="block">{ctaText}</span>
                                </ActionButton>
                                </div>
                                <div className="w-full xs:w-[48%] sm:w-auto">
                                <ActionButton
                                    variant="outline"
                                    icon={false}
                                    onClick={() => {
                                        if (profile?.resume) {
                                            window.open('/download-hero-resume', '_blank');
                                        } else {
                                            alert('Resume file not available for download');
                                        }
                                    }}
                                        className="group relative z-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-[#20B2AA] shadow-sm hover:shadow-md transition-all duration-300 text-gray-800 dark:text-gray-200 hover:text-[#20B2AA] rounded-md w-full text-center flex justify-center items-center text-sm sm:text-base py-3 px-6 sm:px-8"
                                        fullWidth
                                >
                                        <Download className="w-4 sm:w-5 h-4 sm:h-5 mr-1.5 sm:mr-2 text-gray-600 dark:text-gray-400 group-hover:text-[#20B2AA] transition-colors flex-shrink-0" />
                                        <span className="block">{ctaSecondaryText}</span>
                                </ActionButton>
                                </div>
                            </motion.div>

                            <motion.div
                                variants={itemVariants}
                                className="flex items-center gap-4 justify-center mt-3 xs:mt-4"
                            >
                                {githubUrl && (
                                    <motion.a
                                        href={githubUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                        className="w-8 xs:w-10 h-8 xs:h-10 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 group"
                                    >
                                        <Github className="w-4 xs:w-5 h-4 xs:h-5 text-gray-600 dark:text-gray-300 group-hover:text-[#20B2AA] transition-colors duration-300" />
                                    </motion.a>
                                )}
                                {twitterUrl && (
                                    <motion.a
                                        href={twitterUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                        className="w-8 xs:w-10 h-8 xs:h-10 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 group"
                                    >
                                        <Twitter className="w-4 xs:w-5 h-4 xs:h-5 text-gray-600 dark:text-gray-300 group-hover:text-[#20B2AA] transition-colors duration-300" />
                                    </motion.a>
                                )}
                                {linkedinUrl && (
                                    <motion.a
                                        href={linkedinUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                        className="w-8 xs:w-10 h-8 xs:h-10 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 group"
                                    >
                                        <Linkedin className="w-4 xs:w-5 h-4 xs:h-5 text-gray-600 dark:text-gray-300 group-hover:text-[#20B2AA] transition-colors duration-300" />
                                    </motion.a>
                                )}
                                {facebookUrl && (
                                    <motion.a
                                        href={facebookUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                        className="w-8 xs:w-10 h-8 xs:h-10 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 group"
                                    >
                                        <Facebook className="w-4 xs:w-5 h-4 xs:h-5 text-gray-600 dark:text-gray-300 group-hover:text-[#20B2AA] transition-colors duration-300" />
                                    </motion.a>
                                )}
                                {instagramUrl && (
                                    <motion.a
                                        href={instagramUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                        className="w-8 xs:w-10 h-8 xs:h-10 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 group"
                                    >
                                        <Instagram className="w-4 xs:w-5 h-4 xs:h-5 text-gray-600 dark:text-gray-300 group-hover:text-[#20B2AA] transition-colors duration-300" />
                                    </motion.a>
                                )}
                                {dribbbleUrl && (
                                    <motion.a
                                        href={dribbbleUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                        className="w-8 xs:w-10 h-8 xs:h-10 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 group"
                                    >
                                        <svg className="w-4 xs:w-5 h-4 xs:h-5 text-gray-600 dark:text-gray-300 group-hover:text-[#20B2AA] transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 24C5.385 24 0 18.615 0 12S5.385 0 12 0s12 5.385 12 12-5.385 12-12 12zm10.12-10.358c-.35-.11-3.17-.953-6.384-.438 1.34 3.684 1.887 6.684 1.992 7.308 2.3-1.555 3.936-4.02 4.395-6.87zm-6.115 7.808c-.153-.9-.75-4.032-2.19-7.77l-.066.02c-5.79 2.015-7.86 6.025-8.04 6.4 1.73 1.358 3.92 2.166 6.29 2.166 1.42 0 2.77-.29 4-.816zm-11.62-2.58c.232-.4 3.045-5.055 8.332-6.765.135-.045.27-.084.405-.12-.26-.585-.54-1.167-.832-1.74C7.17 11.775 2.206 11.71 1.756 11.7l-.004.312c0 2.633.998 5.037 2.634 6.855zm-2.42-8.955c.46.008 4.683.026 9.477-1.248-1.698-3.018-3.53-5.558-3.8-5.928-2.868 1.35-5.01 3.99-5.676 7.17zM9.6 2.052c.282.38 2.145 2.914 3.822 6 3.645-1.365 5.19-3.44 5.373-3.702-1.81-1.61-4.19-2.586-6.795-2.586-.825 0-1.63.1-2.4.285zm10.335 3.483c-.218.29-1.935 2.493-5.724 4.04.24.49.47.985.68 1.486.08.18.15.36.22.53 3.41-.43 6.8.26 7.14.33-.02-2.42-.88-4.64-2.31-6.38z"/>
                                        </svg>
                                    </motion.a>
                                )}
                                {behanceUrl && (
                                    <motion.a
                                        href={behanceUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                        className="w-8 xs:w-10 h-8 xs:h-10 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 group"
                                    >
                                        <svg className="w-4 xs:w-5 h-4 xs:h-5 text-gray-600 dark:text-gray-300 group-hover:text-[#20B2AA] transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M6.938 4.503c.702 0 1.34.06 1.92.188.577.13 1.07.33 1.485.61.41.28.733.65.96 1.12.225.47.34 1.05.34 1.73 0 .74-.17 1.36-.507 1.86-.338.5-.837.9-1.502 1.22.906.26 1.576.72 2.022 1.37.448.66.665 1.45.665 2.36 0 .75-.13 1.39-.41 1.93-.28.55-.67 1-.17 1.35-.5.35-1.1.6-1.8.76-.69.16-1.45.24-2.28.24H0V4.51h6.938v-.007zM3.495 8.21h2.881c.795 0 1.393-.155 1.795-.465.402-.31.603-.8.603-1.46 0-.43-.083-.78-.25-1.05-.166-.27-.402-.48-.708-.63-.305-.15-.67-.26-1.093-.33-.424-.07-.896-.105-1.416-.105H3.495v4.04zm0 7.32h3.42c.483 0 .928-.05 1.34-.15.41-.1.763-.25 1.058-.45.295-.2.525-.46.69-.78.165-.32.248-.71.248-1.15 0-.48-.073-.89-.22-1.23-.147-.34-.353-.62-.618-.84-.266-.22-.58-.38-.944-.48-.363-.1-.77-.15-1.217-.15H3.495v5.23zm14.776-8.68c.928 0 1.763.156 2.507.47.744.313 1.375.75 1.893 1.312.518.563.918 1.23 1.2 2.003.282.773.423 1.62.423 2.542 0 .135-.007.27-.02.405-.014.135-.035.27-.063.405H16.99c.07.943.35 1.663.84 2.16.49.497 1.17.745 2.04.745.563 0 1.058-.11 1.485-.33.427-.22.77-.5 1.028-.84h2.566c-.49.943-1.185 1.688-2.085 2.235-.9.547-2.015.82-3.345.82-.773 0-1.478-.12-2.115-.36-.637-.24-1.186-.59-1.647-1.05-.46-.46-.816-1.02-1.068-1.68-.252-.66-.378-1.41-.378-2.25 0-.85.126-1.615.378-2.295.252-.68.608-1.26 1.068-1.725.46-.465 1.01-.825 1.647-1.065.637-.24 1.342-.36 2.115-.36zm2.37 4.995c-.07-.838-.35-1.493-.84-1.965-.49-.472-1.12-.708-1.89-.708-.42 0-.798.08-1.133.24-.335.16-.622.38-.861.66-.24.28-.426.61-.558.99-.132.38-.218.79-.258 1.23h5.54v.553zm-8.24-5.39v1.575h4.305v-1.575H12.4z"/>
                                        </svg>
                                    </motion.a>
                                )}
                                {upassignUrl && (
                                    <motion.a
                                        href={upassignUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                        className="w-8 xs:w-10 h-8 xs:h-10 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 group"
                                    >
                                        <svg className="w-4 xs:w-5 h-4 xs:h-5 text-gray-600 dark:text-gray-300 group-hover:text-[#20B2AA] transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8zm-1-13h2v2h-2V7zm0 4h2v6h-2v-6z"/>
                                        </svg>
                                    </motion.a>
                                )}
                                {fiverrUrl && (
                                    <motion.a
                                        href={fiverrUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                        className="w-8 xs:w-10 h-8 xs:h-10 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 group"
                                    >
                                        <svg className="w-4 xs:w-5 h-4 xs:h-5 text-gray-600 dark:text-gray-300 group-hover:text-[#20B2AA] transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12.036 0C5.388 0 0 5.388 0 12.036s5.388 12.036 12.036 12.036 12.036-5.388 12.036-12.036S18.684 0 12.036 0zm5.4 17.1h-2.16v-1.44c-.72.96-1.68 1.68-3.24 1.68-2.4 0-3.96-1.8-3.96-4.32V8.1h2.16v4.56c0 1.44.72 2.4 2.16 2.4 1.44 0 2.88-1.2 2.88-3.12V8.1h2.16v9zm-10.8-9h2.16v9H6.636v-9z"/>
                                        </svg>
                                    </motion.a>
                                )}
                                {upworkUrl && (
                                    <motion.a
                                        href={upworkUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                        className="w-8 xs:w-10 h-8 xs:h-10 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 group"
                                    >
                                        <svg className="w-4 xs:w-5 h-4 xs:h-5 text-gray-600 dark:text-gray-300 group-hover:text-[#20B2AA] transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M18.561 13.158c-1.102 0-2.135-.467-3.074-1.227l.228-1.076.008-.042c.207-1.143.849-3.06 2.839-3.06 1.492 0 2.703 1.212 2.703 2.703-.001 1.489-1.212 2.702-2.704 2.702zm0-8.14c-2.539 0-4.51 1.649-5.31 4.366-1.22-1.834-2.148-4.036-2.687-5.892H7.828v7.112c-.002 1.406-1.141 2.546-2.547 2.548-1.405-.002-2.543-1.143-2.545-2.548V3.492H0v7.112c0 2.914 2.37 5.303 5.281 5.303 2.913 0 5.283-2.389 5.283-5.303v-1.19c.529 1.107 1.182 2.229 1.974 3.221l-1.673 7.873h2.797l1.213-5.71c1.063.679 2.285 1.109 3.686 1.109 3 0 5.439-2.452 5.439-5.45 0-3.002-2.439-5.453-5.439-5.453z"/>
                                        </svg>
                                    </motion.a>
                                )}
                                {freelancerUrl && (
                                    <motion.a
                                        href={freelancerUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                        className="w-8 xs:w-10 h-8 xs:h-10 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 group"
                                    >
                                        <svg className="w-4 xs:w-5 h-4 xs:h-5 text-gray-600 dark:text-gray-300 group-hover:text-[#20B2AA] transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M14.096 3.076c1.634.545 2.929 1.841 3.474 3.474.545 1.634.545 3.365 0 5-.545 1.634-1.841 2.929-3.474 3.474-1.634.545-3.365.545-5 0-1.634-.545-2.929-1.841-3.474-3.474-.545-1.634-.545-3.365 0-5 .545-1.634 1.841-2.929 3.474-3.474 1.634-.545 3.365-.545 5 0zm-2.548 7.924c-.552 0-1-.448-1-1s.448-1 1-1 1 .448 1 1-.448 1-1 1z"/>
                                        </svg>
                                    </motion.a>
                                )}
                                {peopleperhourUrl && (
                                    <motion.a
                                        href={peopleperhourUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                        className="w-8 xs:w-10 h-8 xs:h-10 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 group"
                                    >
                                        <svg className="w-4 xs:w-5 h-4 xs:h-5 text-gray-600 dark:text-gray-300 group-hover:text-[#20B2AA] transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10zm-1-16h2v2h-2V6zm0 4h2v8h-2v-8z"/>
                                        </svg>
                                    </motion.a>
                                )}
                            </motion.div>
                        </motion.div>
                        </div>
                </motion.section>

                {/* Services Section */}
                <section className="py-20 sm:py-24 md:py-32 px-4 bg-white dark:bg-gray-900 relative overflow-hidden" id="services">
                    {/* Subtle background */}
                    <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white dark:from-gray-800/30 dark:to-gray-900/30 opacity-50"></div>

                    <div className="max-w-7xl mx-auto relative">
                        <motion.div 
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6 }}
                            className="text-center mb-16 md:mb-24"
                        >
                            <motion.span
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                                className="text-[#20B2AA] font-medium text-xs sm:text-sm inline-block px-3 sm:px-4 py-1.5 bg-[#E6F7F6] rounded-full mb-3 sm:mb-4"
                            >
                                {servicesContent.badge || 'Professional Services'}
                            </motion.span>
                            <motion.h2
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.3 }}
                                className="text-3xl sm:text-4xl font-bold mt-2 sm:mt-3 mb-3 sm:mb-5"
                            >
                                {servicesContent.title || 'Areas of Expertise'}
                            </motion.h2>
                            <motion.p 
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                                className="text-gray-600 dark:text-gray-300 text-base sm:text-lg max-w-2xl mx-auto px-2"
                            >
                                {servicesContent.description || 'Delivering tailored, high-quality solutions to help your business thrive in the digital landscape'}
                            </motion.p>
                        </motion.div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8">
                            {services.map((service, index) => {
                                // Assign colors based on index
                                const colors = ["#20B2AA", "#8B5CF6", "#3B82F6", "#10B981", "#F59E0B", "#EC4899"];
                                const color = colors[index % colors.length];
                                
                                return (
                                <motion.div
                                    key={service.title}
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    viewport={{ once: true }}
                                    transition={{ duration: 0.5, delay: index * 0.1 }}
                                >
                                    <motion.div 
                                        whileHover={{ y: -5 }}
                                        transition={{ type: "spring", stiffness: 300 }}
                                        className="bg-white dark:bg-gray-800 rounded-xl p-5 sm:p-6 md:p-8 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 dark:border-gray-700 group relative overflow-hidden h-auto sm:h-[260px] md:h-[280px] flex flex-col"
                                    >
                                        {/* Top accent line with color */}
                                        <div className="absolute top-0 left-0 right-0 h-[3px] overflow-hidden">
                                            <motion.div 
                                                initial={{ width: "0%" }}
                                                whileInView={{ width: "100%" }}
                                                viewport={{ once: true }}
                                                transition={{ duration: 0.8, delay: index * 0.1 + 0.3, ease: "easeOut" }}
                                                className="absolute top-0 left-0 h-full" 
                                                style={{ backgroundColor: color }}
                                            ></motion.div>
                                        </div>
                                        
                                        <div className="relative flex-1">
                                            <motion.div 
                                                initial={{ scale: 0.8, opacity: 0 }}
                                                whileInView={{ scale: 1, opacity: 1 }}
                                                viewport={{ once: true }}
                                                transition={{ duration: 0.5, delay: index * 0.1 + 0.4 }}
                                                className="w-12 h-12 sm:w-14 sm:h-14 rounded-full flex items-center justify-center mb-4 sm:mb-6"
                                                style={{ backgroundColor: `${color}10` }}
                                            >
                                                <IconComponent icon={service.icon} className="w-6 h-6 sm:w-7 sm:h-7" style={{ color: color }} />
                                            </motion.div>
                                            <motion.h3 
                                                initial={{ x: -20, opacity: 0 }}
                                                whileInView={{ x: 0, opacity: 1 }}
                                                viewport={{ once: true }}
                                                transition={{ duration: 0.5, delay: index * 0.1 + 0.5 }}
                                                className="text-lg sm:text-xl font-semibold mb-2 sm:mb-3 group-hover:text-[#20B2AA] transition-colors duration-300"
                                            >
                                                {service.title}
                                            </motion.h3>
                                            <motion.p 
                                                initial={{ x: -20, opacity: 0 }}
                                                whileInView={{ x: 0, opacity: 1 }}
                                                viewport={{ once: true }}
                                                transition={{ duration: 0.5, delay: index * 0.1 + 0.6 }}
                                                className="text-gray-600 dark:text-gray-300 text-sm sm:text-base leading-relaxed"
                                            >
                                                {service.description}
                                            </motion.p>
                                        </div>
                                    </motion.div>
                                </motion.div>
                            )})}
                        </div>

                        {/* View All Services Button */}
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6, delay: 0.6 }}
                            className="flex justify-center mt-10 sm:mt-16"
                        >
                            <div className="relative w-full sm:w-auto">
                                <ActionButton 
                                    href="/services" 
                                    variant="minimal" 
                                    icon={false}
                                    className="font-medium text-[#20B2AA] bg-white dark:bg-transparent hover:text-[#1a9994] border border-[#20B2AA]/20 hover:border-[#20B2AA]/60 px-8 py-2.5 rounded-full hover:bg-[#E6F7F6]/50 transition-all shadow-sm hover:shadow-md backdrop-blur-sm overflow-hidden group w-full sm:w-auto text-[15px]"
                                    fullWidth={true}
                                >
                                    <span className="relative z-10">{servicesContent.button_text || 'Explore All Services'}</span>
                                    <motion.span 
                                        initial={{ x: "-100%" }}
                                        whileHover={{ x: "0%" }}
                                        transition={{ duration: 0.4, ease: "easeOut" }}
                                        className="absolute inset-0 bg-gradient-to-r from-[#E6F7F6]/80 to-transparent z-0"
                                    ></motion.span>
                                </ActionButton>
                            </div>
                        </motion.div>
                    </div>
                </section>

                {/* Featured Projects Section */}
                <motion.section 
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6 }}
                    className="py-20 sm:py-24 md:py-32 px-4 sm:px-6 dark:bg-gray-900" 
                    id="works"
                >
                    <div className="max-w-7xl mx-auto">
                        <motion.div 
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6 }}
                            className="text-center mb-12 sm:mb-16"
                        >
                            <motion.span
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                                className="text-[#20B2AA] font-medium inline-block px-3 sm:px-4 py-1.5 bg-[#E6F7F6] rounded-full mb-3 sm:mb-4 text-xs sm:text-sm"
                            >
                                {projectsContent.badge}
                            </motion.span>
                            <motion.h2 
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.3 }}
                                className="text-3xl sm:text-4xl font-bold mt-2 sm:mt-4 mb-3 sm:mb-6"
                            >
                                {projectsContent.title}
                            </motion.h2>
                            <motion.p
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                                className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-base sm:text-lg px-2"
                            >
                                {projectsContent.description}
                            </motion.p>
                        </motion.div>

                        {/* Filter Buttons - Only show if categories exist */}
                        {projectCategories.length > 0 && (
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6 }}
                                className="flex flex-wrap items-center justify-center gap-2 sm:gap-4 mb-8 sm:mb-12"
                            >
                            {/* All projects button */}
                                <motion.button
                                key="All"
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                onClick={() => setActiveFilter('All')}
                                className={`px-4 sm:px-6 py-1.5 sm:py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                                    activeFilter === 'All' 
                                            ? 'bg-[#20B2AA] text-white' 
                                            : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                                    }`}
                                >
                                All
                            </motion.button>

                            {/* Dynamic categories from actual projects only */}
                            {projectCategories.map((category) => (
                                <motion.button
                                    key={category}
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    onClick={() => setActiveFilter(category)}
                                    className={`px-4 sm:px-6 py-1.5 sm:py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                                        activeFilter === category
                                            ? 'bg-[#20B2AA] text-white'
                                            : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                                    }`}
                                >
                                    {category}
                                </motion.button>
                            ))}

                            {/* No additional categories needed - all categories are shown above */}
                            </motion.div>
                        )}

                        {/* Projects Grid */}
                        <motion.div 
                            initial={{ opacity: 0 }}
                            whileInView={{ opacity: 1 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6 }}
                            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8"
                        >
                            {filteredProjects.map((project) => (
                                <ProjectCard
                                    key={project.id}
                                    id={project.id}
                                    title={project.title}
                                    description={project.description}
                                    category={project.category}
                                    image={project.image}
                                    project_url={project.project_url}
                                    github_url={project.github_url}
                                />
                            ))}
                        </motion.div>

                        {/* View All Projects Button */}
                        <motion.div 
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6, delay: 0.4 }}
                            className="flex justify-center mt-10 sm:mt-16 mb-6"
                        >
                            <div className="relative w-full sm:w-auto">
                                <Link href={route('projects')}>
                                <ActionButton 
                                    variant="minimal" 
                                    icon={false}
                                    className="font-medium text-[#20B2AA] bg-white dark:bg-transparent hover:text-[#1a9994] border border-[#20B2AA]/20 hover:border-[#20B2AA]/60 px-8 py-2.5 rounded-full hover:bg-[#E6F7F6]/50 transition-all shadow-sm hover:shadow-md backdrop-blur-sm overflow-hidden group w-full sm:w-auto text-[15px]"
                                    fullWidth={true}
                                >
                                    <span className="relative z-10">{projectsContent.button_text}</span>
                                    <motion.span 
                                        initial={{ x: "-100%" }}
                                        whileHover={{ x: "0%" }}
                                        transition={{ duration: 0.4, ease: "easeOut" }}
                                        className="absolute inset-0 bg-gradient-to-r from-[#E6F7F6]/80 to-transparent z-0"
                                    ></motion.span>
                                </ActionButton>
                                </Link>
                            </div>
                        </motion.div>
                        </div>
                </motion.section>

                {/* Technical Proficiency Section */}
                <motion.section 
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6 }}
                    className="py-20 sm:py-24 px-4 sm:px-6 bg-gradient-to-b from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 relative overflow-hidden" 
                    id="skills"
                >
                    {/* Background Elements */}
                    <div className="absolute inset-0 opacity-30 dark:opacity-10">
                        <motion.div
                            animate={{
                                rotate: [0, 360],
                                scale: [1, 1.2, 1]
                            }}
                            transition={{
                                duration: 20,
                                repeat: Infinity,
                                ease: "linear"
                            }}
                            className="absolute top-20 right-20 w-24 sm:w-40 h-24 sm:h-40 border-2 border-[#20B2AA] rounded-full"
                        />
                        <motion.div
                            animate={{
                                rotate: [360, 0],
                                scale: [1, 1.1, 1]
                            }}
                            transition={{
                                duration: 15,
                                repeat: Infinity,
                                ease: "linear"
                            }}
                            className="absolute bottom-40 left-20 w-20 sm:w-32 h-20 sm:h-32 border-2 border-purple-500 rounded-lg transform rotate-45"
                        />
                        </div>

                    <div className="max-w-7xl mx-auto relative">
                        <motion.div 
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6 }}
                            className="text-center mb-12 sm:mb-16"
                        >
                            <motion.span
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                                className="text-[#20B2AA] font-medium inline-block px-3 sm:px-4 py-1.5 bg-[#E6F7F6] rounded-full mb-3 sm:mb-4 text-xs sm:text-sm"
                            >
                                {skillsContent.badge}
                            </motion.span>
                            <motion.h2
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.3 }}
                                className="text-3xl sm:text-4xl font-bold mt-2 sm:mt-4 mb-3 sm:mb-6"
                            >
                                {skillsContent.title}
                            </motion.h2>
                            <motion.p
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                                className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-base sm:text-lg px-2"
                            >
                                {skillsContent.description}
                            </motion.p>
                        </motion.div>

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 lg:gap-20">
                            {/* Left Column - Skill Bars */}
                            <motion.div 
                                initial={{ opacity: 0, x: -50 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6 }}
                                className="space-y-6 sm:space-y-8"
                            >
                                {skills.progress.map((skill, index) => (
                                    <motion.div
                                        key={skill.label}
                                        initial={{ opacity: 0, x: -50 }}
                                        whileInView={{ opacity: 1, x: 0 }}
                                        viewport={{ once: true }}
                                        transition={{ duration: 0.5, delay: index * 0.1 }}
                                    >
                                        <ProgressBar label={skill.label} percentage={skill.percentage} />
                                    </motion.div>
                                ))}
                            </motion.div>

                            {/* Right Column - Skill Cards */}
                            <motion.div 
                                initial={{ opacity: 0, x: 50 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6 }}
                                className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-8 mt-4 lg:mt-0"
                            >
                                {skills.card.map((skill, index) => (
                                    <motion.div
                                        key={skill.skill}
                                        initial={{ opacity: 0, y: 20 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        viewport={{ once: true }}
                                        transition={{ duration: 0.5, delay: index * 0.1 }}
                                        whileHover={{ scale: 1.05 }}
                                    >
                                        <SkillCard skill={skill.skill} percentage={skill.percentage} />
                                    </motion.div>
                                ))}
                            </motion.div>
                            </div>
                        </div>
                </motion.section>

                {/* Experience & Education Section */}
                <motion.section
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6 }}
                    className="py-20 sm:py-24 px-4 sm:px-6 dark:bg-gray-900 relative overflow-hidden"
                    id="resume"
                >
                    {/* Background Elements */}
                    <div className="absolute inset-0">
                        <motion.div
                            animate={{
                                y: [0, -20, 0],
                                opacity: [0.1, 0.2, 0.1]
                            }}
                            transition={{
                                duration: 5,
                                repeat: Infinity,
                                ease: "easeInOut"
                            }}
                            className="absolute top-20 left-10 sm:left-20 w-48 sm:w-64 h-48 sm:h-64 bg-[#20B2AA]/5 rounded-full blur-3xl"
                        />
                        <motion.div
                            animate={{
                                y: [0, 20, 0],
                                opacity: [0.1, 0.2, 0.1]
                            }}
                            transition={{
                                duration: 7,
                                repeat: Infinity,
                                ease: "easeInOut"
                            }}
                            className="absolute bottom-20 right-10 sm:right-20 w-56 sm:w-96 h-56 sm:h-96 bg-purple-500/5 rounded-full blur-3xl"
                        />
                        </div>

                    <div className="max-w-7xl mx-auto relative">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6 }}
                            className="text-center mb-12 sm:mb-16"
                        >
                            <motion.span
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                                className="text-[#20B2AA] font-medium inline-block px-3 sm:px-4 py-1.5 bg-[#E6F7F6] rounded-full mb-3 sm:mb-4 text-xs sm:text-sm"
                            >
                                {resumeContent.section_badge}
                            </motion.span>
                            <motion.h2
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.3 }}
                                className="text-3xl sm:text-4xl font-bold mt-2 sm:mt-4 mb-3 sm:mb-6"
                            >
                                {resumeContent.section_title}
                            </motion.h2>
                            <motion.p
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                                className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-base sm:text-lg px-2"
                            >
                                {resumeContent.section_description}
                            </motion.p>
                        </motion.div>

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20">
                            {/* Experience Column */}
                            <motion.div
                                initial={{ opacity: 0, x: -50 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6 }}
                            >
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    viewport={{ once: true }}
                                    transition={{ duration: 0.6 }}
                                    className="flex items-center gap-3 mb-8 sm:mb-12"
                                >
                                    <motion.div
                                        whileHover={{ scale: 1.1, rotate: 360 }}
                                        transition={{ duration: 0.6 }}
                                        className="w-10 h-10 sm:w-12 sm:h-12 bg-[#E6F7F6] rounded-xl flex items-center justify-center"
                                    >
                                        <Briefcase className="w-5 h-5 sm:w-6 sm:h-6 text-[#20B2AA]" />
                                    </motion.div>
                                    <h3 className="text-xl sm:text-2xl font-semibold">{resumeContent.experience_title}</h3>
                                </motion.div>

                                <div className="space-y-6">
                                    {experiences.map((experience, index) => (
                                        <motion.div
                                            key={experience.id}
                                            initial={{ opacity: 0, x: -50 }}
                                            whileInView={{ opacity: 1, x: 0 }}
                                            viewport={{ once: true }}
                                            transition={{ duration: 0.5, delay: index * 0.1 }}
                                        >
                                            <TimelineItem
                                                period={experience.formatted_period}
                                                title={experience.title}
                                                organization={experience.company}
                                                description={experience.description}
                                            />
                                        </motion.div>
                                    ))}
                                </div>
                            </motion.div>

                            {/* Education Column */}
                            <motion.div
                                initial={{ opacity: 0, x: 50 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6 }}
                                className="mt-8 lg:mt-0"
                            >
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    whileInView={{ opacity: 1, y: 0 }}
                                    viewport={{ once: true }}
                                    transition={{ duration: 0.6 }}
                                    className="flex items-center gap-3 mb-8 sm:mb-12"
                                >
                                    <motion.div
                                        whileHover={{ scale: 1.1, rotate: 360 }}
                                        transition={{ duration: 0.6 }}
                                        className="w-10 h-10 sm:w-12 sm:h-12 bg-[#E6F7F6] rounded-xl flex items-center justify-center"
                                    >
                                        <GraduationCap className="w-5 h-5 sm:w-6 sm:h-6 text-[#20B2AA]" />
                                    </motion.div>
                                    <h3 className="text-xl sm:text-2xl font-semibold">{resumeContent.education_title}</h3>
                                </motion.div>

                                <div className="space-y-6">
                                    {education.map((edu, index) => (
                                        <motion.div
                                            key={edu.id}
                                            initial={{ opacity: 0, x: 50 }}
                                            whileInView={{ opacity: 1, x: 0 }}
                                            viewport={{ once: true }}
                                            transition={{ duration: 0.5, delay: index * 0.1 }}
                                        >
                                            <TimelineItem
                                                period={edu.formatted_period}
                                                title={edu.degree}
                                                organization={edu.institution}
                                                description={edu.description || ''}
                                            />
                                        </motion.div>
                                    ))}
                                </div>
                            </motion.div>
                        </div>

                        {/* Download Resume Button */}
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6, delay: 0.4 }}
                            className="flex justify-center mt-10 sm:mt-16"
                        >
                            <motion.button
                                whileHover={{ scale: 1.05, y: -2 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => {
                                    if (resumeContent.resume_file_path) {
                                        window.open('/download-resume', '_blank');
                                    } else {
                                        alert('Resume file not available for download');
                                    }
                                }}
                                className="bg-[#20B2AA] hover:bg-[#1a9994] text-white px-6 sm:px-8 py-4 sm:py-6 text-sm sm:text-base inline-flex items-center gap-2 rounded-xl group relative overflow-hidden cursor-pointer w-full sm:w-auto justify-center sm:justify-start"
                            >
                                <span className="relative z-10 flex items-center gap-2">
                                <Download className="w-4 h-4 sm:w-5 sm:h-5" />
                                {resumeContent.download_button_text}
                                </span>
                                <div className="absolute inset-0 bg-white/20 transform -skew-x-12 translate-x-full group-hover:translate-x-0 transition-transform duration-500"></div>
                            </motion.button>
                        </motion.div>
                        </div>
                </motion.section>

                {/* Testimonials Section */}
                <motion.section 
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6 }}
                    className="py-20 sm:py-24 px-4 sm:px-6 bg-gradient-to-br from-gray-50 via-white to-gray-50 dark:from-gray-800 dark:via-gray-900 dark:to-gray-800 relative overflow-hidden" 
                    id="testimonials"
                >
                    {/* Animated background elements */}
                    <div className="absolute inset-0">
                        <motion.div
                            animate={{
                                scale: [1, 1.2, 1],
                                opacity: [0.1, 0.2, 0.1],
                            }}
                            transition={{
                                duration: 8,
                                repeat: Infinity,
                                ease: "easeInOut"
                            }}
                            className="absolute top-20 left-10 sm:left-20 w-64 sm:w-96 h-64 sm:h-96 bg-[#20B2AA]/10 rounded-full blur-3xl"
                        />
                        <motion.div
                            animate={{
                                scale: [1, 1.3, 1],
                                opacity: [0.1, 0.15, 0.1],
                            }}
                            transition={{
                                duration: 10,
                                repeat: Infinity,
                                ease: "easeInOut",
                                delay: 1
                            }}
                            className="absolute bottom-20 right-10 sm:right-20 w-64 sm:w-[30rem] h-64 sm:h-[30rem] bg-purple-500/10 rounded-full blur-3xl"
                        />
                        </div>

                    <div className="max-w-7xl mx-auto relative">
                        <motion.div 
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6 }}
                            className="text-center mb-12 sm:mb-16"
                        >
                            <motion.span
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                                className="text-[#20B2AA] font-medium inline-block px-3 sm:px-4 py-1.5 bg-[#E6F7F6] rounded-full mb-3 sm:mb-4 text-xs sm:text-sm"
                            >
                                {testimonialsContent.badge}
                            </motion.span>
                            <motion.h2 
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.3 }}
                                className="text-3xl sm:text-4xl font-bold mt-2 sm:mt-4 mb-3 sm:mb-6"
                            >
                                {testimonialsContent.title}
                            </motion.h2>
                            <motion.p 
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                                className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-base sm:text-lg px-2"
                            >
                                {testimonialsContent.description}
                            </motion.p>
                        </motion.div>

                        <motion.div
                            initial={{ opacity: 0 }}
                            whileInView={{ opacity: 1 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6 }}
                        >
                            <TestimonialGrid
                                testimonials={testimonials}
                                autoPlay={true}
                                autoPlayInterval={5000}
                                className="max-w-6xl mx-auto"
                            />
                        </motion.div>
                    </div>
                </motion.section>

                {/* Contact Section */}
                <motion.section 
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6 }}
                    className="py-20 sm:py-24 px-4 sm:px-6 bg-white dark:bg-gray-900 relative overflow-hidden" 
                    id="contact"
                >
                    {/* Background Elements */}
                    <div className="absolute inset-0">
                        <motion.div
                            animate={{
                                y: [0, -30, 0],
                                opacity: [0.1, 0.2, 0.1],
                            }}
                            transition={{
                                duration: 8,
                                repeat: Infinity,
                                ease: "easeInOut"
                            }}
                            className="absolute top-20 left-10 sm:left-20 w-64 md:w-[40rem] h-64 md:h-[40rem] bg-[#20B2AA]/5 rounded-full blur-3xl"
                        />
                        <motion.div
                            animate={{
                                y: [0, 30, 0],
                                opacity: [0.1, 0.15, 0.1],
                            }}
                            transition={{
                                duration: 10,
                                repeat: Infinity,
                                ease: "easeInOut",
                                delay: 1
                            }}
                            className="absolute bottom-20 right-10 sm:right-20 w-64 md:w-[35rem] h-64 md:h-[35rem] bg-purple-500/5 rounded-full blur-3xl"
                        />
                        </div>

                    <div className="max-w-7xl mx-auto relative">
                        <motion.div 
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            viewport={{ once: true }}
                            transition={{ duration: 0.6 }}
                            className="text-center mb-12 sm:mb-16"
                        >
                            <motion.span
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                                className="text-[#20B2AA] font-medium inline-block px-3 sm:px-4 py-1.5 bg-[#E6F7F6] rounded-full mb-3 sm:mb-4 text-xs sm:text-sm"
                            >
                                {profile.contact?.section_badge || 'Contact'}
                            </motion.span>
                            <motion.h2
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.3 }}
                                className="text-3xl sm:text-4xl font-bold mt-2 sm:mt-4 mb-3 sm:mb-6"
                            >
                                {profile.contact?.section_title || 'Get In Touch'}
                            </motion.h2>
                            <motion.p
                                initial={{ opacity: 0, y: 10 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6, delay: 0.4 }}
                                className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto text-base sm:text-lg px-2"
                            >
                                {profile.contact?.section_description || 'Have a project in mind? Let us discuss how I can help bring your ideas to life.'}
                            </motion.p>
                        </motion.div>

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
                            {/* Contact Form */}
                            <motion.div
                                initial={{ opacity: 0, x: -50 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6 }}
                                className="bg-white dark:bg-gray-800 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                <form onSubmit={handleContactSubmit} className="space-y-5 sm:space-y-6">
                                    <motion.div
                                        initial={{ opacity: 0, y: 20 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        viewport={{ once: true }}
                                        transition={{ duration: 0.5 }}
                                        className="space-y-3 sm:space-y-4"
                                    >
                                    <div>
                                            <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5 sm:mb-2">
                                            {profile.contact?.form_labels?.name_label || 'Name'}
                                        </label>
                                            <motion.input
                                                whileFocus={{ scale: 1.01 }}
                                            type="text"
                                            id="name"
                                            name="name"
                                            value={contactData.name}
                                            onChange={(e) => setContactData('name', e.target.value)}
                                            placeholder={profile.contact?.form_labels?.name_placeholder || 'Your Name'}
                                            required
                                                className={`w-full px-4 py-2.5 sm:py-3 rounded-lg border ${contactErrors.name ? 'border-red-500' : 'border-gray-200 dark:border-gray-700'} focus:border-[#20B2AA] focus:ring-2 focus:ring-[#20B2AA] focus:ring-opacity-20 outline-none transition-all duration-200 bg-white dark:bg-gray-900 dark:text-white`}
                                        />
                                        {contactErrors.name && (
                                            <p className="text-red-500 text-sm mt-1">{contactErrors.name}</p>
                                        )}
                                    </div>
                                    <div>
                                            <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5 sm:mb-2">
                                            {profile.contact?.form_labels?.email_label || 'Email'}
                                        </label>
                                            <motion.input
                                                whileFocus={{ scale: 1.01 }}
                                            type="email"
                                            id="email"
                                            name="email"
                                            value={contactData.email}
                                            onChange={(e) => setContactData('email', e.target.value)}
                                            placeholder={profile.contact?.form_labels?.email_placeholder || '<EMAIL>'}
                                            required
                                                className={`w-full px-4 py-2.5 sm:py-3 rounded-lg border ${contactErrors.email ? 'border-red-500' : 'border-gray-200 dark:border-gray-700'} focus:border-[#20B2AA] focus:ring-2 focus:ring-[#20B2AA] focus:ring-opacity-20 outline-none transition-all duration-200 bg-white dark:bg-gray-900 dark:text-white`}
                                        />
                                        {contactErrors.email && (
                                            <p className="text-red-500 text-sm mt-1">{contactErrors.email}</p>
                                        )}
                                    </div>
                                    <div>
                                            <label htmlFor="subject" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5 sm:mb-2">
                                            {profile.contact?.form_labels?.subject_label || 'Subject'}
                                        </label>
                                            <motion.input
                                                whileFocus={{ scale: 1.01 }}
                                            type="text"
                                            id="subject"
                                            name="subject"
                                            value={contactData.subject}
                                            onChange={(e) => setContactData('subject', e.target.value)}
                                            placeholder={profile.contact?.form_labels?.subject_placeholder || 'Project Subject'}
                                            required
                                                className={`w-full px-4 py-2.5 sm:py-3 rounded-lg border ${contactErrors.subject ? 'border-red-500' : 'border-gray-200 dark:border-gray-700'} focus:border-[#20B2AA] focus:ring-2 focus:ring-[#20B2AA] focus:ring-opacity-20 outline-none transition-all duration-200 bg-white dark:bg-gray-900 dark:text-white`}
                                        />
                                        {contactErrors.subject && (
                                            <p className="text-red-500 text-sm mt-1">{contactErrors.subject}</p>
                                        )}
                                    </div>
                                    <div>
                                            <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5 sm:mb-2">
                                            {profile.contact?.form_labels?.message_label || 'Message'}
                                        </label>
                                            <motion.textarea
                                                whileFocus={{ scale: 1.01 }}
                                            id="message"
                                            name="message"
                                            rows={5}
                                            value={contactData.message}
                                            onChange={(e) => setContactData('message', e.target.value)}
                                            placeholder={profile.contact?.form_labels?.message_placeholder || 'Your Message'}
                                            required
                                                className={`w-full px-4 py-2.5 sm:py-3 rounded-lg border ${contactErrors.message ? 'border-red-500' : 'border-gray-200 dark:border-gray-700'} focus:border-[#20B2AA] focus:ring-2 focus:ring-[#20B2AA] focus:ring-opacity-20 outline-none transition-all duration-200 resize-none bg-white dark:bg-gray-900 dark:text-white`}
                                        />
                                        {contactErrors.message && (
                                            <p className="text-red-500 text-sm mt-1">{contactErrors.message}</p>
                                        )}
                                    </div>
                                    </motion.div>

                                    <motion.button
                                        whileHover={{ scale: contactProcessing ? 1 : 1.02 }}
                                        whileTap={{ scale: contactProcessing ? 1 : 0.98 }}
                                        type="submit"
                                        disabled={contactProcessing}
                                        className={`w-full py-2.5 sm:py-3 px-4 ${contactProcessing ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#20B2AA] hover:bg-[#1a9994] cursor-pointer'} text-white font-medium rounded-lg shadow-lg shadow-[#20B2AA]/20 hover:shadow-xl hover:shadow-[#20B2AA]/30 transition-all duration-200`}
                                    >
                                        {contactProcessing ? 'Sending...' : (profile.contact?.form_labels?.submit_button_text || 'Send Message')}
                                    </motion.button>
                                </form>
                            </motion.div>

                            {/* Contact Info */}
                            <motion.div
                                initial={{ opacity: 0, x: 50 }}
                                whileInView={{ opacity: 1, x: 0 }}
                                viewport={{ once: true }}
                                transition={{ duration: 0.6 }}
                                className="space-y-4 sm:space-y-6 mt-2 lg:mt-0"
                            >
                                {[
                                    {
                                        icon: <Mail className="w-5 h-5 sm:w-6 sm:h-6 text-[#20B2AA]" />,
                                        title: profile.contact?.contact_labels?.email_label || "Email",
                                        subtitle: profile.contact?.contact_labels?.email_subtitle || "For general inquiries:",
                                        value: profile.contact?.email,
                                        delay: 0
                                    },
                                    {
                                        icon: <Phone className="w-5 h-5 sm:w-6 sm:h-6 text-[#20B2AA]" />,
                                        title: profile.contact?.contact_labels?.phone_label || "Phone",
                                        subtitle: profile.contact?.contact_labels?.phone_subtitle || "Available Monday-Friday:",
                                        value: profile.contact?.phone,
                                        delay: 0.2
                                    },
                                    {
                                        icon: <MapPin className="w-5 h-5 sm:w-6 sm:h-6 text-[#20B2AA]" />,
                                        title: profile.contact?.contact_labels?.location_label || "Location",
                                        subtitle: profile.contact?.contact_labels?.location_subtitle || "Based in:",
                                        value: profile.contact?.location,
                                        delay: 0.4
                                    }
                                ].map((item, index) => (
                                    <motion.div
                                        key={item.title}
                                        initial={{ opacity: 0, y: 20 }}
                                        whileInView={{ opacity: 1, y: 0 }}
                                        viewport={{ once: true }}
                                        transition={{ duration: 0.5, delay: item.delay }}
                                        className="bg-white dark:bg-gray-800 rounded-xl p-5 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 group"
                                    >
                                        <div className="flex items-center gap-3 sm:gap-4">
                                            <motion.div
                                                whileHover={{ scale: 1.1, rotate: 360 }}
                                                transition={{ duration: 0.6 }}
                                                className="w-10 h-10 sm:w-12 sm:h-12 bg-[#E6F7F6] rounded-xl flex items-center justify-center"
                                            >
                                                {item.icon}
                                            </motion.div>
                                            <div>
                                                <h3 className="text-base sm:text-lg font-semibold mb-0.5 sm:mb-1 dark:text-white group-hover:text-[#20B2AA] transition-colors">
                                                    {item.title}
                                                </h3>
                                                <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-0.5 sm:mb-1">
                                                    {item.subtitle}
                                                </p>
                                                <p className="text-[#20B2AA] font-medium text-sm sm:text-base">
                                                    {item.value}
                                                </p>
                            </div>
                        </div>
                                    </motion.div>
                                ))}
                            </motion.div>
                </div>
                    </div>
                </motion.section>

                {/* Dynamic Footer */}
                <DynamicFooter footerData={footerData} />
            </div>
        </ThemeProvider>
    );
}

function formatTitle(title: string): string {
    // Check if the title has the default format
    if (title === 'Creative Designer & Developer') {
        return `Creative <span class="text-[#20B2AA] relative">
            Designer
            <svg class="absolute -bottom-1 xs:-bottom-2 left-0 w-full" viewBox="0 0 200 8" fill="none">
                <path d="M0 4C50 4 150 4 200 4" stroke="#20B2AA" stroke-width="4" stroke-linecap="round"/>
            </svg>
        </span> & Developer`;
    }
    
    // If it's a custom title, look for keywords to highlight
    const keywords = ['Designer', 'Developer', 'Engineer', 'Programmer', 'Creative', 'UX/UI', 'Architect'];
    let formattedTitle = title;
    
    for (const keyword of keywords) {
        if (title.includes(keyword)) {
            formattedTitle = title.replace(
                keyword, 
                `<span class="text-[#20B2AA] relative">
                    ${keyword}
                    <svg class="absolute -bottom-1 xs:-bottom-2 left-0 w-full" viewBox="0 0 200 8" fill="none">
                        <path d="M0 4C50 4 150 4 200 4" stroke="#20B2AA" stroke-width="4" stroke-linecap="round"/>
                    </svg>
                </span>`
            );
            break;
        }
    }
    
    return formattedTitle;
}
