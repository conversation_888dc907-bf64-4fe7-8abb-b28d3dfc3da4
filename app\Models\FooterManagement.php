<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class FooterManagement extends Model
{
    use HasFactory;

    protected $table = 'footer_management';

    protected $fillable = [
        // Footer Content
        'description',
        'copyright_text',

        // Footer Links
        'footer_links',

        // Settings
        'show_social_links',
        'show_footer_links',
        'show_scroll_to_top',
    ];

    protected $casts = [
        'footer_links' => 'array',
        'show_social_links' => 'boolean',
        'show_footer_links' => 'boolean',
        'show_scroll_to_top' => 'boolean',
    ];

    /**
     * Get the footer management instance or create default one
     */
    public static function getOrCreate()
    {
        try {
            // Check if table exists
            if (!Schema::hasTable('footer_management')) {
                self::createTable();
            }

            $footer = self::first();

            if (!$footer) {
                $footer = self::create(self::getDefaults());
            }

            return $footer;
        } catch (\Exception $e) {
            // If there's still an error, try to create table and data
            self::createTable();
            return self::first() ?: self::create(self::getDefaults());
        }
    }

    /**
     * Create the footer_management table
     */
    private static function createTable()
    {
        if (!Schema::hasTable('footer_management')) {
            DB::statement("
                CREATE TABLE footer_management (
                    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
                    description TEXT NOT NULL DEFAULT 'Creating exceptional digital experiences through innovative design and development solutions.',
                    copyright_text VARCHAR(255) NOT NULL DEFAULT '© {year}. All rights reserved.',
                    footer_links JSON NULL,
                    show_social_links TINYINT(1) NOT NULL DEFAULT 1,
                    show_footer_links TINYINT(1) NOT NULL DEFAULT 1,
                    show_scroll_to_top TINYINT(1) NOT NULL DEFAULT 1,
                    created_at TIMESTAMP NULL,
                    updated_at TIMESTAMP NULL
                )
            ");
        }
    }

    /**
     * Get default footer management data
     */
    public static function getDefaults()
    {
        return [
            'description' => 'Creating exceptional digital experiences through innovative design and development solutions.',
            'copyright_text' => '© {year}. All rights reserved.',
            'footer_links' => [
                ['title' => 'Privacy Policy', 'url' => '#', 'enabled' => true],
                ['title' => 'Terms of Service', 'url' => '#', 'enabled' => true],
                ['title' => 'Cookie Policy', 'url' => '#', 'enabled' => true],
            ],
            'show_social_links' => true,
            'show_footer_links' => true,
            'show_scroll_to_top' => true,
        ];
    }

    /**
     * Get formatted copyright text with year
     */
    public function getFormattedCopyrightAttribute()
    {
        return str_replace(
            '{year}',
            date('Y'),
            $this->copyright_text
        );
    }



    /**
     * Get enabled footer links (maximum 3)
     */
    public function getEnabledFooterLinksAttribute()
    {
        if (!$this->footer_links) {
            return [];
        }

        $enabledLinks = array_filter($this->footer_links, function ($link) {
            return isset($link['enabled']) && $link['enabled'];
        });

        // Ensure maximum 3 enabled links
        return array_slice($enabledLinks, 0, 3);
    }
}
