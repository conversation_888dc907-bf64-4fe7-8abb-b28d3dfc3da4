import { motion } from 'framer-motion';
import { ArrowUp, Twitter, Github, Linkedin, Facebook, Instagram, Dribbble, Briefcase, Globe, User } from 'lucide-react';
import { DynamicLogo } from './ui/dynamic-logo';

interface FooterLink {
    title: string;
    url: string;
    enabled: boolean;
}

interface SocialLink {
    name: string;
    url: string;
    icon: string;
    color: string;
}

interface FooterData {
    logo: {
        text: string;
        type: string;
        icon: string;
        icon_type: string;
        image: string | null;
        color: string;
    };
    description: string;
    copyright: string;
    company_name: string;
    social_links: SocialLink[];
    footer_links: FooterLink[];
    settings: {
        show_social_links: boolean;
        show_footer_links: boolean;
        show_scroll_to_top: boolean;
    };
}

interface Props {
    footerData: FooterData;
}

const iconMap: { [key: string]: any } = {
    Twitter,
    Github,
    Linkedin,
    Facebook,
    Instagram,
    Dribbble,
    UPassign: Globe,
    Fiverr: Briefcase,
    Upwork: Briefcase,
    Freelancer: User,
    PeoplePerHour: User,
    <PERSON>hance: <PERSON><PERSON><PERSON>,
};

export function DynamicFooter({ footerData }: Props) {
    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const renderLogo = () => {
        const { logo } = footerData;

        return (
            <DynamicLogo
                logoText={logo.text}
                logoType={logo.type as 'text_only' | 'icon_only' | 'text_with_icon'}
                logoIcon={logo.icon}
                logoIconType={logo.icon_type as 'letter' | 'svg' | 'image'}
                logoImage={logo.image}
                logoColor={logo.color}
                className="scale-90 sm:scale-100"
            />
        );
    };

    const renderSocialIcon = (iconName: string) => {
        const IconComponent = iconMap[iconName];
        return IconComponent ? <IconComponent className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" /> : null;
    };

    return (
        <footer className="bg-[#0F172A] dark:bg-gray-950 text-white py-8 sm:py-12">
            <div className="max-w-7xl mx-auto px-4 sm:px-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-12 mb-6 sm:mb-8">
                    {/* Left Column - Logo and Description */}
                    <div>
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6 }}
                            viewport={{ once: true }}
                            className="flex items-center gap-2 mb-3 sm:mb-4"
                        >
                            {renderLogo()}
                        </motion.div>

                        <motion.p
                            className="text-gray-400 max-w-md text-xs sm:text-sm"
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.1 }}
                            viewport={{ once: true }}
                        >
                            {footerData.description}
                        </motion.p>
                    </div>

                    {/* Right Column - Social Links */}
                    {footerData.settings.show_social_links && footerData.social_links.length > 0 && (
                        <div className="flex justify-start sm:justify-end mt-4 sm:mt-0">
                            <motion.div
                                className="flex items-center gap-3 sm:gap-4"
                                initial={{ opacity: 0, y: 20 }}
                                whileInView={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.6, delay: 0.2 }}
                                viewport={{ once: true }}
                            >
                                {footerData.social_links.map((social, index) => (
                                    <motion.a
                                        key={social.name}
                                        href={social.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-gray-800 hover:bg-gray-700 transition-colors"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                        initial={{ opacity: 0, scale: 0 }}
                                        whileInView={{ opacity: 1, scale: 1 }}
                                        transition={{ duration: 0.3, delay: 0.3 + index * 0.1 }}
                                        viewport={{ once: true }}
                                    >
                                        {renderSocialIcon(social.icon)}
                                    </motion.a>
                                ))}
                            </motion.div>
                        </div>
                    )}
                </div>

                {/* Divider */}
                <div className="h-px bg-gray-800 mb-6 sm:mb-8"></div>

                {/* Bottom Row */}
                <div className="flex flex-col sm:flex-row items-center justify-between gap-4 sm:gap-0">
                    {/* Copyright */}
                    <motion.div
                        className="text-gray-400 text-xs sm:text-sm order-2 sm:order-1"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6, delay: 0.4 }}
                        viewport={{ once: true }}
                    >
                        {footerData.copyright}
                    </motion.div>

                    {/* Footer Links */}
                    {footerData.settings.show_footer_links && footerData.footer_links.length > 0 && (
                        <motion.div
                            className="flex items-center flex-wrap justify-center gap-4 sm:gap-8 order-1 sm:order-2"
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: 0.3 }}
                            viewport={{ once: true }}
                        >
                            {footerData.footer_links.map((link, index) => (
                                <motion.a
                                    key={index}
                                    href={link.url}
                                    className="text-gray-400 text-xs sm:text-sm hover:text-[#20B2AA] transition-colors"
                                    whileHover={{ scale: 1.05 }}
                                    initial={{ opacity: 0, x: -20 }}
                                    whileInView={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
                                    viewport={{ once: true }}
                                >
                                    {link.title}
                                </motion.a>
                            ))}
                        </motion.div>
                    )}
                </div>
            </div>

            {/* Scroll to Top Button */}
            {footerData.settings.show_scroll_to_top && (
                <motion.button
                    onClick={scrollToTop}
                    className="fixed bottom-8 right-8 p-3 bg-[#20B2AA] text-white rounded-full shadow-lg hover:bg-[#1a9994] transition-colors duration-300 z-50"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                >
                    <ArrowUp className="w-5 h-5" />
                </motion.button>
            )}
        </footer>
    );
}
